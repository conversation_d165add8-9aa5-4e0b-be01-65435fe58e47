import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, Image, ScrollView, TouchableOpacity, Animated, useWindowDimensions, Platform } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { Colors } from '../../constants/Colors';
// import ThemedCard from '../../components/ThemedCard';
import GradientGlassButton from '../../components/GradientGlassButton';
import ThemedText from '../../components/ThemedText';
import ThemedView from '../../components/ThemedView';
import StudyRecommendCarousel from "../StudyRecommendCarousel";
import StudyRecommendDetailModal from "../StudyRecommendDetailModal";
import { LinearGradient } from 'expo-linear-gradient';
import CustomSwitch from '../../components/CustomSwitch';
import PersonaAnimation from '../../components/PersonaAnimation';
import { shadowPresets } from '../../utils/shadowUtils';

const AiImg = require('../../assets/dogStandingOnPodium.png');
// const CAROUSEL_TOP = 450; // 轮播区初始距离顶部的像素值，可根据实际UI调整

export default function Plans() {
  const params = useLocalSearchParams();

  useEffect(() => {
    console.log('到达计划页面');
  }, []);

  // 初始化activeTab状态，根据URL参数或默认为0（人物画像）
  const [activeTab, setActiveTab] = useState(() => {
    // 在初始化时检查URL参数
    if (params.tab !== undefined && params.tab !== null && params.tab !== '') {
      // 支持字符串类型的tab参数
      if (params.tab === 'plans') {
        return 0;
      }
      // 也支持数字类型的tab参数（向后兼容）
      const tabIndex = parseInt(params.tab, 10);
      if (!isNaN(tabIndex) && tabIndex >= 0 && tabIndex <= 2) {
        return tabIndex;
      }
    }
    return 0;
  });

  // 监听 URL 参数变化，更新 activeTab
  useEffect(() => {
    if (params.tab !== undefined && params.tab !== null && params.tab !== '') {
      // 支持字符串类型的tab参数
      if (params.tab === 'plans') {
        setActiveTab(0);
        return;
      }

      // 也支持数字类型的tab参数（向后兼容）
      const tabIndex = parseInt(params.tab, 10);
      if (!isNaN(tabIndex) && tabIndex >= 0 && tabIndex <= 2) {
        setActiveTab(tabIndex);
      } else {
        setActiveTab(0);
      }
    } else {
      // 确保默认为人物画像tab
      setActiveTab(0);
    }
  }, [params.tab]);

  const [modalVisible, setModalVisible] = useState(false);
  const [modalCard, setModalCard] = useState(null); // {item, layout}
  const anim = useRef(new Animated.Value(0)).current;
  const [lockedIndex, setLockedIndex] = useState(null); // 新增
  const [hiddenIndex, setHiddenIndex] = useState(null); // 新增


  // 轮播卡片点击回调
  const handleCardPress = (item, layout) => {
    setModalCard({ item, layout });
    setModalVisible(true);
    setLockedIndex(layout.carouselIndex ?? null); // 记录被点击的卡片index
    setHiddenIndex(layout.carouselIndex ?? null); // 动画期间彻底隐藏该卡片
    anim.setValue(0);
    Animated.timing(anim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: Platform.OS !== 'web',
    }).start();
  };
  const handleModalClose = () => {
    setModalVisible(false);
    setModalCard(null);
    // 不立即清空lockedIndex/hiddenIndex，等动画结束后再清空
    anim.setValue(0);
  };
  const handleModalAfterClose = () => {
    setLockedIndex(null);
    setHiddenIndex(null);
  };

  // activeTab已在上面声明

  // PersonaAnimation展开状态管理
  const [personaExpanded, setPersonaExpanded] = useState(false);
  // const personaAnimRef = useRef(null);

  // 页面滚动相关
  const scrollViewRef = useRef(null);
  const scrollY = useRef(new Animated.Value(0)).current;
  const lastScrollY = useRef(0);
  const scrollDirection = useRef('down'); // 'up' 或 'down'

  // 标签移动动画
  const tagTranslateY = useRef(new Animated.Value(0)).current;

  // 滚动阈值设置
  const SCROLL_DOWN_THRESHOLD = 80; // 向下滚动多少像素后触发收起
  const SCROLL_UP_THRESHOLD = 30; // 向上滚动回到多少像素后触发展开

  // 处理PersonaAnimation展开/收起 - 同步动画
  const handlePersonaExpand = () => {
    console.log('Plans页面 - 展开PersonaAnimation，标签下移');
    setPersonaExpanded(true);
    // 标签下移动画（展开时标签下移）- 增加移动距离
    Animated.timing(tagTranslateY, {
      toValue: 80, // 增加下移距离：50 -> 80
      duration: 600,
      useNativeDriver: Platform.OS !== 'web',
    }).start();
  };

  const handlePersonaCollapse = () => {
    console.log('Plans页面 - 收起PersonaAnimation，标签上移');
    setPersonaExpanded(false);
    // 标签上移动画（收起时标签上移）- 增加移动距离
    Animated.timing(tagTranslateY, {
      toValue: -50, // 增加上移距离：-30 -> -50
      duration: 600,
      useNativeDriver: Platform.OS !== 'web',
    }).start();
  };

  // 手动点击展开（从收起状态展开，标签不移动或轻微下移）
  const handleManualExpand = () => {
    console.log('Plans页面 - 手动点击展开');
    setPersonaExpanded(true);
    // 手动展开时标签保持原位或轻微下移
    Animated.timing(tagTranslateY, {
      toValue: 0, // 回到原位
      duration: 600,
      useNativeDriver: Platform.OS !== 'web',
    }).start();
  };

  // 处理滚动事件 - 增强移动端兼容性
  const handleScroll = (event) => {
    try {
      const currentY = event.nativeEvent.contentOffset.y;
      const deltaY = currentY - lastScrollY.current;

      // 添加更多调试信息
      console.log('滚动事件触发:', {
        currentY: Math.round(currentY),
        deltaY: Math.round(deltaY),
        activeTab,
        personaExpanded,
        timestamp: Date.now()
      });

      // 只在人物画像tab时处理
      if (activeTab !== 0) {
        console.log('不在人物画像tab，跳过处理');
        return;
      }

      // 更新滚动方向（只有明显滚动时才更新）
      if (Math.abs(deltaY) > 2) { // 降低阈值，提高敏感度
        scrollDirection.current = deltaY > 0 ? 'down' : 'up';
        console.log('更新滚动方向:', scrollDirection.current);
      }

      // 新的动画逻辑：
      // 1. 向下滚动超过阈值 -> 收起PersonaAnimation，标签上移
      // 2. 向上滚动回到顶部 -> 展开PersonaAnimation，标签下移

      if (currentY > SCROLL_DOWN_THRESHOLD && scrollDirection.current === 'down' && personaExpanded) {
        // 向下滚动超过阈值且当前是展开状态 -> 收起
        console.log('触发收起：向下滚动超过阈值', { currentY, threshold: SCROLL_DOWN_THRESHOLD });
        handlePersonaCollapse();
      } else if (currentY < SCROLL_UP_THRESHOLD && scrollDirection.current === 'up' && !personaExpanded) {
        // 向上滚动回到顶部且当前是收起状态 -> 展开
        console.log('触发展开：向上滚动回到顶部', { currentY, threshold: SCROLL_UP_THRESHOLD });
        handlePersonaExpand();
      }

      lastScrollY.current = currentY;
    } catch (error) {
      console.error('滚动事件处理错误:', error);
    }
  };

  // 备用滚动处理 - 在滚动结束时触发
  const handleScrollEnd = (event) => {
    console.log('滚动结束事件触发');
    handleScroll(event);
  };

  // 动量滚动结束处理
  const handleMomentumScrollEnd = (event) => {
    console.log('动量滚动结束事件触发');
    handleScroll(event);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return (
          <PersonaContent
            personaExpanded={personaExpanded}
            onPersonaExpand={handleManualExpand} // 手动点击使用不同的处理函数
            onPersonaCollapse={handlePersonaCollapse}
            tagTranslateY={tagTranslateY}
          />
        );
      case 1:
        return <AIPathContent onCardPress={handleCardPress} anim={anim} modalVisible={modalVisible} lockedIndex={lockedIndex} hiddenIndex={hiddenIndex} />;
      case 2:
        return <GoalSettingContent />;
      default:
        return (
          <PersonaContent
            personaExpanded={personaExpanded}
            onPersonaExpand={handleManualExpand} // 手动点击使用不同的处理函数
            onPersonaCollapse={handlePersonaCollapse}
            tagTranslateY={tagTranslateY}
          />
        );
    }
  };



  return (
    <ThemedView style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={[styles.content, { paddingTop: 32 }]}
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          {
            useNativeDriver: false,
            listener: handleScroll
          }
        )}
        scrollEventThrottle={16}
        // 添加备用滚动事件处理
        onScrollEndDrag={handleScrollEnd}
        onMomentumScrollEnd={handleMomentumScrollEnd}
        // 添加这些属性确保在移动端正常工作
        bounces={true}
        alwaysBounceVertical={true}
        scrollEnabled={true}
        nestedScrollEnabled={true}
        // 强制启用滚动事件
        scrollsToTop={false}
      >
          {/* 顶部Tab */}
          <View style={styles.tabRow}>
            <TouchableOpacity onPress={() => setActiveTab(0)}>
              <ThemedText style={[styles.tab, activeTab === 0 && styles.tabActive]}>人物画像</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setActiveTab(1)}>
              <ThemedText style={[styles.tab, activeTab === 1 && styles.tabActive]}>AI路径规划</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setActiveTab(2)}>
              <ThemedText style={[styles.tab, activeTab === 2 && styles.tabActive]}>目标设定</ThemedText>
            </TouchableOpacity>
          </View>

          {/* 调试按钮 - 仅在人物画像tab显示 */}
          {/* {activeTab === 0 && (
            <View style={{ flexDirection: 'row', justifyContent: 'center', marginVertical: 10 }}>
              <TouchableOpacity
                onPress={handlePersonaExpand}
                style={{ backgroundColor: '#4CAF50', padding: 10, marginHorizontal: 5, borderRadius: 5 }}
              >
                <ThemedText style={{ color: 'white', fontSize: 12 }}>测试展开</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handlePersonaCollapse}
                style={{ backgroundColor: '#f44336', padding: 10, marginHorizontal: 5, borderRadius: 5 }}
              >
                <ThemedText style={{ color: 'white', fontSize: 12 }}>测试收起</ThemedText>
              </TouchableOpacity>
            </View>
          )} */}
          {renderTabContent()}
          {/* 全屏弹窗 */}
          <StudyRecommendDetailModal
            visible={modalVisible}
            card={modalCard}
            onClose={handleModalClose}
            onAfterClose={handleModalAfterClose}
          />
        </ScrollView>
    </ThemedView>
  );
}

// AI路径规划页面内容
const AIPathContent = ({ onCardPress, anim, modalVisible, lockedIndex, hiddenIndex }) => {
  const { width } = useWindowDimensions();
  const [weeklyPlanSwitch, setWeeklyPlanSwitch] = useState(false);
  const [rightSwitch, setRightSwitch] = useState(false);
  return (
    <>
      {/* 顶部栏+Tab已由父组件实现 */}
      {/* 顶部日期+日历图标 */}
      <View style={{ flexDirection: 'row', alignItems: 'center', marginHorizontal: 8, marginTop: 8 }}>
        <View style={{ backgroundColor: Colors.lightPurple, borderRadius: 18, paddingHorizontal: 16, paddingVertical: 6, marginRight: 8 }}>
          <ThemedText style={{ color: '#444', fontSize: 15 }}>Nov,2023</ThemedText>
        </View>
        <Image source={require('../../assets/Plan_image/Calendar.png')} style={{ width: 22, height: 22 }} />
        <View style={{ backgroundColor: Colors.lightPurple, borderRadius: 18, paddingHorizontal: 16, paddingVertical: 6, flexDirection: 'row', alignItems: 'center', marginLeft: 'auto' }}>
          <ThemedText style={{ color: '#444', fontSize: 15 }}>Weekly</ThemedText>
          <View style={{ width: 18, height: 18, backgroundColor: '#ccc', borderRadius: 9, marginLeft: 8 }} />
        </View>
      </View>
      {/* 周统计卡片+下方内容整体大白色圆角盒子 */}
      <View style={{
        backgroundColor: Colors.lightBlue, // 使用Colors.js中的浅蓝色
        borderRadius: 32,
        margin: 16,
        padding: 0,
        overflow: 'hidden'
      }}>
        {/* 深色统计卡片 */}
        <LinearGradient
          colors={['#E2DCF1', '#CDEAFA']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={{
            padding: 15,
            paddingBottom: 20,
            borderRadius: 32
          }}
        >
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-end' }}>
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((d, i) => (
              <View key={d} style={{ alignItems: 'center', flex: 1 }}>
                <View style={{ width: 34, height: [80, 120, 60, 160, 90, 70, 110][i], backgroundColor: '#fff', borderRadius: 14, marginBottom: 0, justifyContent: 'flex-end', alignItems: 'center', position: 'relative' }}>
                  <ThemedText style={{ color: '#444', fontSize: 13, position: 'absolute', bottom: 4, left: 0, right: 0, textAlign: 'center' }}>{d}</ThemedText>
                </View>
              </View>
            ))}
          </View>
        </LinearGradient>
        {/* 下方内容（不设置圆角） */}
        <View style={{
          backgroundColor: '#fff',
          padding: 10,
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',

        }}>
          {/* 左侧 */}
          <View style={{ flexDirection: 'column', alignItems: 'center' }}>
            <CustomSwitch
              value={weeklyPlanSwitch}
              onValueChange={setWeeklyPlanSwitch}
              text="这周的学习计划"
              trackColorOff="#FFD29B"
              trackColorOn="#FDAC54"
              width={140}
              height={32}
            />
            <View style={{ flexDirection: 'row', marginTop: 8 }}>
              <ThemedText style={{ color: '#444', fontSize: 13, marginRight: 8 }}>this week</ThemedText>
              <ThemedText style={{ color: '#444', fontSize: 13 }}>Last week</ThemedText>
            </View>
          </View>
          {/* 右侧 */}
          <View style={{ flexDirection: 'column', alignItems: 'flex-end' }}>
            <CustomSwitch
              value={rightSwitch}
              onValueChange={setRightSwitch}
              text="通知"
              trackColorOff="#FFD29B"
              trackColorOn="#FDAC54"
              width={80}
              height={28}
            />
          </View>
        </View>
      </View>
      {/* 学习推荐标题 */}
      <ThemedText style={{ fontSize: 22, fontWeight: 'bold', color: '#222', marginLeft: 16 }}>学习推荐</ThemedText>
      {/* 推荐卡片轮播组件 */}
      <View style={{ minHeight: 260 }}>
        <StudyRecommendCarousel width={width} onCardPress={onCardPress} modalVisible={modalVisible} lockedIndex={lockedIndex} hiddenIndex={hiddenIndex} />
      </View>
    </>
  );
};

// 人物画像页面内容 - 更新版本
const PersonaContent = ({ personaExpanded, onPersonaExpand, onPersonaCollapse, tagTranslateY }) => {
  return (
    <View style={styles.personaContainer}>
      {/* 动画人物画像组件 */}
      <View style={styles.animationContainer}>
        <PersonaAnimation
          avatarSource={AiImg}
          isExpanded={personaExpanded}
          onExpand={onPersonaExpand}
          onCollapse={onPersonaCollapse}
          rotateOnExpand={true}
        />
      </View>

      {/* 功能卡片区域 - 整体标签容器，添加移动动画 */}
      <Animated.View
        style={[
          styles.cardsSection,
          {
            transform: [{ translateY: tagTranslateY }]
          }
        ]}
      >
        {/* 整体内容区域 */}
        <View style={styles.containerBody}>
          {/* 整体容器顶部白色标题块 */}
          <View style={styles.containerHeader}>
            
          </View>
          {/* 定制化策略卡片 */}
          <View style={styles.featureCard}>
            <View style={styles.cardIcon}>
              <Image source={require('../../assets/Plan_image/Suggestion.png')} style={styles.iconImage} resizeMode="contain" />
            </View>
            <View style={styles.cardContent}>
              <ThemedText style={styles.cardTitle}>定制化解决方案建议</ThemedText>
              <ThemedText style={styles.cardSubtitle}>定制专属用户档案，选择你的学习方式</ThemedText>

              {/* 流程步骤图 */}
              <View style={styles.processImageContainer}>
                <Image source={require('../../assets/Plan_image/graph.png')} style={styles.processImage} resizeMode="contain" />
              </View>

              <ThemedText style={styles.cardDescription}>
                • 采用「反馈学习法」：从错题智能求助的技能项目推荐学习路径
              </ThemedText>
            </View>
          </View>

          {/* 需求分析卡片 */}
          <View style={styles.featureCard}>
            <View style={styles.cardIcon}>
              <Image source={require('../../assets/Plan_image/Crying_face.png')} style={styles.iconImage} resizeMode="contain" />
            </View>
            <View style={styles.cardContent}>
              <ThemedText style={styles.cardTitle}>需求分析</ThemedText>
              <ThemedText style={styles.cardSubtitle}>用户遇到的问题正在需要解决的分析方</ThemedText>

              <View style={styles.analysisPoints}>
                <ThemedText style={styles.pointText}>1.信息收集：高频切换学习平台，收集远多于手实践</ThemedText>
                <ThemedText style={styles.pointText}>2.深度模式：容易陷入"准备阶段"，导致 完美方案"出现</ThemedText>
                <ThemedText style={styles.pointText}>3.反馈依赖：需要即时的成就感来维持学习动力</ThemedText>
              </View>
            </View>
          </View>

          {/* 学习模式特征卡片 */}
          <View style={styles.featureCard}>
            <View style={styles.cardIcon}>
              <Image source={require('../../assets/Plan_image/Glasses.png')} style={styles.iconImage} resizeMode="contain" />
            </View>
            <View style={styles.cardContent}>
              <ThemedText style={styles.cardTitle}>学习模式特征</ThemedText>

              <View style={styles.analysisPoints}>
                <ThemedText style={styles.pointText}>1.思维可视化倾向：思维导图法反馈验证化认知框架信息好</ThemedText>
                <ThemedText style={styles.pointText}>2.学习触发：未来"互动性"原来知识的思维力理解</ThemedText>
                <ThemedText style={styles.pointText}>3.时间管理来：双重标准"改不下来"高示范行为系统</ThemedText>
              </View>
            </View>
          </View>
        </View>
      </Animated.View>
    </View>
  );
};

// 目标设定页面内容（按图片重新设计）
const GoalSettingContent = () => (
  <>
    {/* 吉祥物与欢迎语 */}
    <View style={styles.topRow}>
      <Image source={AiImg} style={styles.AiImg} />
      <View style={{ flex: 1, marginLeft: 16, position: 'relative', zIndex: 1 }}>
        <ThemedText style={styles.hi}>Hi，用户12345678</ThemedText>
        <ThemedText style={styles.welcome}>快来设定你的目标吧~</ThemedText>
        <ThemedText style={styles.tip}>3步获取制定计划，提升3倍学习效率</ThemedText>
      </View>
      <View style={styles.arrowContainer}>
        <Image
          source={require('../../assets/Plan_image/arrowUpper.png')}
          style={[styles.arrowImage, { resizeMode: 'contain' }]}
        />
      </View>
    </View>

    {/* 主要目标设定卡片 */}
    <View style={styles.goalSettingCard}>
      <ThemedText style={styles.goalCardTitle}>小艾根据你的选择为你定制了个性化目标</ThemedText>

      {/* 短期目标 */}
      <View style={styles.goalSectionMerged}>
        <View style={styles.goalTitleBox}>
          <ThemedText style={styles.goalSectionTitleInside}>短期目标</ThemedText>
        </View>
        <View style={styles.goalItemInside}>
          <Image source={require('../../assets/Plan_image/Target.png')} style={styles.goalIconImage} />
          <ThemedText style={styles.goalText}>考试</ThemedText>
        </View>
      </View>

      {/* 长期目标 */}
      <View style={styles.goalSectionMerged}>
        <View style={styles.goalTitleBox}>
          <ThemedText style={styles.goalSectionTitleInside}>长期目标</ThemedText>
        </View>
        <View style={styles.goalItemInside}>
          <Image source={require('../../assets/Plan_image/Target.png')} style={styles.goalIconImage} />
          <ThemedText style={styles.goalText}>英语四六级</ThemedText>
        </View>
      </View>

      {/* 修改当前目标按钮 */}
      <GradientGlassButton
        title="修改当前目标"
        onPress={() => router.push('/plan_goal_edit')}
        style={styles.modifyGoalButton}
        gradientColors={['#FFEEDB', '#ffb87e']}
        borderColor="rgba(255, 194, 121, 0.5)"
        blurBackgroundColor="rgba(255, 238, 219, 0.3)"
        textColor="#7A3C10"
        borderRadius={24}
      />
    </View>

    {/* 名言卡片 */}
    <View style={styles.quoteCard}>
      <ThemedText style={styles.quote}>计划是行动的蓝图，没有精心绘制的蓝图，行动就会迷失方向。</ThemedText>
      <ThemedText style={styles.quoteAuthor}>——雷军</ThemedText>
    </View>

    {/* 底部卡片区 */}
    <View style={styles.bottomRow}>
      <View style={styles.bottomCard}>
        {/* 水波纹渐变背景 */}
        <View style={styles.waveGradientContainer}>
          <View style={[styles.waveCircle, styles.wave1]} />
          <View style={[styles.waveCircle, styles.wave2]} />
          <View style={[styles.waveCircle, styles.wave3]} />
          <View style={[styles.waveCircle, styles.wave4]} />
        </View>
        <View style={styles.bottomCardTopRow}>
          <View style={styles.bottomStarBox}>
            <Image source={require('../../assets/Plan_image/Star.png')} style={styles.bottomStarIcon} />
            <ThemedText style={styles.bottomStarText}>4.9</ThemedText>
          </View>
          <View style={styles.bottomArrowBox}>
            <Image source={require('../../assets/Plan_image/topRightArrow.png')} style={styles.bottomArrowIcon} />
          </View>
        </View>
        <View style={styles.bottomCardContent}>
          <ThemedText style={styles.bottomTitle}>目标案例参考</ThemedText>
          <ThemedText style={styles.bottomDesc}>英语备考雅思7分{"\n"}三个月掌握Python基础</ThemedText>
        </View>
      </View>
      <View style={styles.bottomCard}>
        {/* 水波纹渐变背景 */}
        <View style={styles.waveGradientContainer}>
          <View style={[styles.waveCircle, styles.wave1]} />
          <View style={[styles.waveCircle, styles.wave2]} />
          <View style={[styles.waveCircle, styles.wave3]} />
          <View style={[styles.waveCircle, styles.wave4]} />
        </View>
        <View style={styles.bottomCardTopRow}>
          <View style={styles.bottomStarBox}>
            <Image source={require('../../assets/Plan_image/Star.png')} style={styles.bottomStarIcon} />
            <ThemedText style={styles.bottomStarText}>4.9</ThemedText>
          </View>
          <View style={styles.bottomArrowBox}>
            <Image source={require('../../assets/Plan_image/topRightArrow.png')} style={styles.bottomArrowIcon} />
          </View>
        </View>
        <View style={styles.bottomCardContent}>
          <ThemedText style={styles.bottomTitle}>成果展示</ThemedText>
          <ThemedText style={styles.bottomDesc}>上周80%用户自定目标计划，平均提升分15分</ThemedText>
        </View>
      </View>
    </View>
  </>
);



const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: Colors.background, paddingHorizontal: 16 },
  content: { paddingBottom: 120 },
  tabRow: { flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', margin: 10, gap: 40 },
  tab: { fontSize: 17, color: '#888', paddingBottom: 4, marginBottom: 2, marginTop: 16 },
  tabActive: { color: '#222', fontWeight: 'bold', borderBottomWidth: 3, borderColor: '#222', fontSize: 18 },
  topRow: { flexDirection: 'row', alignItems: 'center', padding: 16, position: 'relative' },
  AiImg: { width: 100, height: 100 },
  hi: { fontSize: 15, color: '#444', marginBottom: 2 },
  welcome: { fontSize: 15, color: '#222', fontWeight: 'bold' },
  tip: { fontSize: 12, color: '#bbb', marginTop: 2 },
  arrowContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 60,
    position: 'absolute',
    right: 20,
    top: 40,
    zIndex: 0,
  },
  arrowImage: {
    width: 90,
    height: 90,
    opacity: 0.6,
  },
  // 卡片样式
  goalSettingCard: {
    backgroundColor: '#FFD29B', // 使用指定的橙色背景
    borderRadius: 24,
    margin: 16,
    padding: 24,
    minHeight: 320, // 增加高度以容纳新内容
    ...shadowPresets.heavy,
  },
  goalCardTitle: {
    fontSize: 16,
    color: '#7A3C10',
    marginBottom: 24,
    fontWeight: 'bold',
    textAlign: 'left',
    lineHeight: 22,
  },
  goalSection: {
    marginBottom: 20,
  },
  goalSectionMerged: {
    marginBottom: 20,
  },
  goalSectionTitle: {
    lineHeight: 22,
    fontSize: 14,
    color: '#000000',
    marginBottom: 12,
    fontWeight: '600',
    backgroundColor: '#FFEEDB',
  },
  goalSectionTitleInside: {
    fontSize: 14,
    color: '#7A3C10',
    fontWeight: '600',
    lineHeight: 22,
  },
  goalTitleBox: {
    backgroundColor: '#FFEEDB',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 0,
    alignSelf: 'flex-start',
    ...shadowPresets.medium,
  },
  goalItemInside: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEEDB',
    borderTopLeftRadius: 0,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginTop: -1,
    width: '100%',
    // 移除阴影以实现与goalTitleBox的无缝融合
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 4,
    // elevation: 1,
  },
  goalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    ...shadowPresets.medium,
  },
  goalItemMerged: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEEDB',
    borderRadius: 12,
    padding: 16,
    ...shadowPresets.medium,
  },
  goalIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FFE7CE',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  goalIconImage: {
    width: 24,
    height: 24,
    marginRight: 12,
    resizeMode: 'contain',
  },
  goalIconText: {
    fontSize: 14,
  },
  goalText: {
    fontSize: 16,
    color: '#7A3C10',
    fontWeight: '500',
  },
  modifyGoalButton: {
    width: '70%',
    height: 48,
    borderRadius: 24,
    marginTop: 16,
    marginBottom: 0,
    alignSelf: 'center',
  },
  mainProgressCard: {
    backgroundColor: '#FFD29B', // 使用指定的橙色背景
    borderRadius: 24,
    margin: 16,
    padding: 24,
    minHeight: 280, // 增加高度以匹配图片比例
    alignItems: 'center',
    justifyContent: 'space-between',
    boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
    elevation: 4,
  },
  progressCard: {
    backgroundColor: Colors.lightBlue, // 使用Colors.js中的浅蓝色
    borderRadius: 18,
    margin: 16,
    padding: 16,
    alignItems: 'center',
    // 内阴影模拟
    boxShadow: '-2px -2px 6px rgba(255, 255, 255, 1)',
    elevation: 2,
    // 外阴影
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  quoteCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 20,
    alignItems: 'flex-start',
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginTop: 8,
    gap: 30
  },
  bottomCard: {
    flex: 1,
    backgroundColor: '#FFEEDB', // 使用最浅的渐变色作为基础背景
    borderRadius: 20,
    padding: 16,
    minHeight: 140, // 增加高度以匹配图片比例
    position: 'relative',
    overflow: 'hidden', // 确保水波纹不超出卡片边界
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  bottomCardTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 46,
    zIndex: 10, // 确保在水波纹之上
    position: 'relative',
  },
  waveGradientContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
    zIndex: 1, // 确保在背景之上，内容之下
  },
  waveCircle: {
    position: 'absolute',
    borderRadius: 100, // 确保是圆形
  },
  wave1: {
    width: 80,
    height: 80,
    backgroundColor: '#FED7AB',
    top: -25,
    left: -25,
    zIndex: 4, // 最上层
    opacity: 0.9,
  },
  wave2: {
    width: 100,
    height: 100,
    backgroundColor: '#FDE1C1',
    top: -20,
    left: -20,
    zIndex: 3,
    opacity: 0.8,
  },
  wave3: {
    width: 120,
    height: 120,
    backgroundColor: '#FFE8CE',
    top: -15,
    left: -15,
    zIndex: 2,
    opacity: 0.7,
  },
  wave4: {
    width: 140,
    height: 140,
    backgroundColor: '#FFEEDB',
    top: -55,
    left: -55,
    zIndex: 1, // 最底层
    opacity: 0.6,
  },
  bottomStarBox: {
    backgroundColor: '#FFC279', // 半透明白色背景
    borderRadius: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomStarIcon: {
    width: 11.2, // 16 * 0.7
    height: 11.2, // 16 * 0.7
    marginRight: 2.8, // 4 * 0.7
  },
  bottomStarText: {
    fontSize: 9.8, // 14 * 0.7
    color: '#333',
    fontWeight: '600',
  },
  bottomArrowBox: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)', // 半透明白色背景
    borderRadius: 16,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomArrowIcon: {
    width: 16,
    height: 16,
  },
  bottomCardContent: {
    alignItems: 'flex-start',
    width: '100%',
    marginBottom: 8,
    marginTop: 20,
    flex: 1,
    justifyContent: 'flex-end',
    zIndex: 10, // 确保在水波纹之上
    position: 'relative',
  },
  bottomTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginBottom: 6,
    textAlign: 'left',
  },
  bottomDesc: {
    fontSize: 11,
    color: '#FF8C42', // 使用橙色文字
    textAlign: 'left',
    lineHeight: 15,
    fontWeight: '400',
  },

  // 按钮样式
  setBtn: {
    backgroundColor: Colors.button, // 使用Colors.js中的按钮颜色
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginTop: 24,
    ...shadowPresets.medium,
  },
  setBtnText: { color: '#000000', fontSize: 16 },
  progressTitle: { fontSize: 18, color: '#000000', marginBottom: 8, fontWeight: 'bold' },
  progressDesc: { fontSize: 14, color: '#666', marginBottom: 24, textAlign: 'center', lineHeight: 20 },
  progressBarContainer: { width: '100%', marginBottom: 32, marginTop: 16 },
  progressBarRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginBottom: 16 },
  progressLabels: { flexDirection: 'row', justifyContent: 'space-between', paddingHorizontal: 16 },
  progressLabel: { fontSize: 12, color: '#8B4513', textAlign: 'center', flex: 1, fontWeight: '500' },
  progressDot: { width: 20, height: 20, borderRadius: 10, backgroundColor: '#ffffff', zIndex: 2 },
  progressDotActive: { backgroundColor: '#D2691E' },
  progressLine: { width: 80, height: 3, backgroundColor: '#ffffff', marginHorizontal: -2 },
  progressLineActive: { backgroundColor: '#D2691E' },
  setGoalButton: {
    width: '80%',
    height: 48,
    borderRadius: 24,
    marginTop: 8,
    marginBottom: 0,
  },
  quote: { fontSize: 14, color: '#333', textAlign: 'left', lineHeight: 20, marginBottom: 8 },
  quoteAuthor: { fontSize: 13, color: '#666', alignSelf: 'flex-end' },

  // 人物画像页面样式
  personaContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 30, // 增加到顶部tab的边距
  },
  avatarSection: {
    height: 320,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  // 虚线圆圈背景
  dashedCircle: {
    position: 'absolute',
    width: 280,
    height: 280,
    borderRadius: 140,
    borderWidth: 2,
    borderColor: '#8B4513',
    borderStyle: 'dashed',
  },
  tagsContainer: {
    position: 'absolute',
    width: 320,
    height: 320,
  },
  tagCircle: {
    position: 'absolute',
    backgroundColor: '#FFFFFF',
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: '#8B4513',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tagText: {
    fontSize: 11,
    color: '#8B4513',
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 14,
    paddingHorizontal: 4,
  },
  // 动画容器样式
  animationContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10, // 减少垂直边距：20 -> 10，让人物画像与tab更近
  },

  // 小圆点装饰
  smallDot: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4A90E2',
    borderWidth: 2,
    borderColor: '#4A90E2',
  },
  dotTopLeft: { top: 20, left: 80 },
  dotTopRight: { top: 20, right: 80 },
  dotRight: { top: '50%', right: 30, marginTop: -6 },
  dotBottomRight: { bottom: 20, right: 80 },
  dotBottomLeft: { bottom: 20, left: 80 },
  dotLeft: { top: '50%', left: 30, marginTop: -6 },

  // 虚线小圆圈
  dashedSmallCircle: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#8B4513',
    borderStyle: 'dashed',
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dashedCircleText: {
    fontSize: 10,
    color: '#8B4513',
    textAlign: 'center',
  },
  dashedCircle1: { top: 80, left: 120 },
  dashedCircle2: { top: 120, right: 60 },
  dashedCircle3: { bottom: 80, left: 80 },

  centerAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#D2691E',
    ...shadowPresets.heavy,
  },
  avatarImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },

  // 功能卡片样式 - 整体标签容器设计
  cardsSection: {
    flex: 1,
    marginTop: 10, // 减少上边距：20 -> 10，让功能卡片区域与人物画像更近
  },
  containerHeader: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingHorizontal: 30,
    paddingVertical: 8,
    alignSelf: 'center',
  },
  containerHeaderText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#7A3C10',
  },
  containerBody: {
    backgroundColor: '#FFEEDB',
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 100, // 延伸到底部
    borderRadius: 24,
    marginTop: -8, // 与顶部标题块重叠连接
  },
  featureCard: {
    backgroundColor: 'transparent',
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
  },
  cardIcon: {
    width: 50,
    height: 50,
    borderRadius: 8, // 方形带圆角
    backgroundColor: '#FFE4B5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  iconImage: {
    width: 30,
    height: 30,
  },
  cardContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#7A3C10',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#8B4513',
    marginBottom: 8,
  },
  processImageContainer: {
    alignItems: 'center',
    marginBottom: 8,
  },
  processImage: {
    width: '100%',
    height: 60,
  },
  cardDescription: {
    fontSize: 12,
    color: '#8B4513',
    lineHeight: 16,
  },
  analysisPoints: {
    marginTop: 8,
  },
  pointText: {
    fontSize: 12,
    color: '#8B4513',
    lineHeight: 16,
    marginBottom: 4,
  },
});