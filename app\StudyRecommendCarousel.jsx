import React, { useRef, useState, useEffect } from 'react';
import { View, Animated, FlatList, StyleSheet, Dimensions, TouchableOpacity, findNodeHandle, UIManager, Platform } from 'react-native';
import ThemedCard from '../components/ThemedCard';
import ThemedText from '../components/ThemedText';
import { LinearGradient } from 'expo-linear-gradient';

export default function StudyRecommendCarousel({ width, onCardPress, modalVisible, lockedIndex, hiddenIndex }) {
  const CARD_WIDTH = width * 0.45; // 更窄
  const CARD_HEIGHT = 280;
  const CARD_SPACING = 12;
  const SIDE_SPACE = (width - CARD_WIDTH) / 2;

  const rawData = [
    {
      title: '考试规划',
      desc: '根据考试时间安排学习计划和任务',
    },
    {
      title: '学习规划',
      desc: '规划学习路径，提升效率',
    },
    {
      title: '兴趣推荐',
      desc: '根据兴趣推荐相关课程',
    },
  ];

  // 定义每张卡片的渐变色
  const cardGradients = [
    ['#FFE2BD', '#E5F5FF'], // 第一张卡片：FFE2BD到E5F5FF
    ['#F1E2FF', '#FFE2BD'], // 第二张卡片：F1E2FF到FFE2BD
    ['#E8F7DE', '#FFE2BD'], // 第三张卡片：E8F7DE到FFE2BD
  ];

  const DATA_LENGTH = rawData.length;
  const loopData = [...rawData, ...rawData, ...rawData];
  const LOOP_OFFSET = DATA_LENGTH;

  // 获取卡片渐变色的函数
  const getCardGradient = (index) => {
    const realIndex = index % DATA_LENGTH;
    return cardGradients[realIndex] || ['#e3e3e3', '#ababab'];
  };

  const scrollX = useRef(new Animated.Value(0)).current;
  const cardRefs = useRef([]);
  const flatListRef = useRef();
  const [centerIndex, setCenterIndex] = useState(LOOP_OFFSET); // 默认中间
  const [hideOthers, setHideOthers] = useState(false);
  const [hideAll, setHideAll] = useState(false); // 控制所有卡片消失
  const savedScrollPosition = useRef(0); // 保存点击时的滚动位置
  const savedCenterIndex = useRef(LOOP_OFFSET); // 保存点击时的中心索引
  
  // App端性能优化：预计算动画值
  const getInitialScrollOffset = () => LOOP_OFFSET * (CARD_WIDTH + CARD_SPACING);
  
  // 初始滚动到中间，width变化时也要重置
  useEffect(() => {
    const initialOffset = getInitialScrollOffset();
    setTimeout(() => {
      flatListRef.current?.scrollToOffset({
        offset: initialOffset,
        animated: false,
      });
      savedScrollPosition.current = initialOffset;
      savedCenterIndex.current = LOOP_OFFSET;
      scrollX.setValue(initialOffset);
    }, 0);
  }, [width]); // 依赖width

  // 弹窗关闭时重置隐藏状态，并恢复到原来的位置
  useEffect(() => {
    if (!modalVisible && hideAll) {
      setHideOthers(false);
      setHideAll(false);
    }
  }, [modalVisible, hideAll]);

  // hideAll变为false时，恢复到上次保存的位置
  useEffect(() => {
    if (!hideAll && flatListRef.current && savedScrollPosition.current !== undefined) {
      flatListRef.current.scrollToOffset({
        offset: savedScrollPosition.current,
        animated: false,
      });
      setCenterIndex(savedCenterIndex.current);
      scrollX.setValue(savedScrollPosition.current);
    }
  }, [hideAll]);

  // 监听滚动，动态计算当前居中卡片索引
  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { x: scrollX } } }],
    {
      useNativeDriver: Platform.OS !== 'web', // 确保在原生线程执行
      listener: (event) => {
        // 实时更新centerIndex和保存位置
        const offsetX = event.nativeEvent.contentOffset.x;
        const itemWidth = CARD_WIDTH + CARD_SPACING;
        const currentIndex = Math.round(offsetX / itemWidth);

        // 更新保存的位置信息
        savedScrollPosition.current = offsetX;

        if (currentIndex !== centerIndex) {
          setCenterIndex(currentIndex);
          savedCenterIndex.current = currentIndex;
        }
      }
    }
  );

  // 无限循环：滑到边界时跳回中间，并更新centerIndex
  const handleMomentumScrollEnd = (e) => {
    const offsetX = e.nativeEvent.contentOffset.x;
    const itemWidth = CARD_WIDTH + CARD_SPACING;
    let index = Math.round(offsetX / itemWidth);
    
    // 更新保存的位置信息
    savedScrollPosition.current = offsetX;
    savedCenterIndex.current = index;
    
    if (index < DATA_LENGTH) {
      const newOffset = (index + DATA_LENGTH) * itemWidth;
      flatListRef.current?.scrollToOffset({
        offset: newOffset,
        animated: false,
      });
      setCenterIndex(index + DATA_LENGTH);
      savedScrollPosition.current = newOffset;
      savedCenterIndex.current = index + DATA_LENGTH;
    } else if (index >= DATA_LENGTH * 2) {
      const newOffset = (index - DATA_LENGTH) * itemWidth;
      flatListRef.current?.scrollToOffset({
        offset: newOffset,
        animated: false,
      });
      setCenterIndex(index - DATA_LENGTH);
      savedScrollPosition.current = newOffset;
      savedCenterIndex.current = index - DATA_LENGTH;
    } else {
      setCenterIndex(index);
      savedScrollPosition.current = offsetX;
      savedCenterIndex.current = index;
    }
  };

  const handlePress = async (item, index) => {
    if (!onCardPress) return;

    // 计算当前滚动位置对应的中心卡片索引
    const itemWidth = CARD_WIDTH + CARD_SPACING;
    const offsetX = scrollX._value;
    const center = offsetX / itemWidth;
    // 精确计算当前卡片的scale
    let cardScale = 0.9;
    if (Math.abs(index - center) < 0.01) {
      cardScale = 1;
    } else if (index < center) {
      cardScale = 0.9 + 0.1 * (index + 1 - center);
    } else if (index > center) {
      cardScale = 0.9 + 0.1 * (center + 1 - index);
    }

    const ref = cardRefs.current[index];
    if (ref) {
      if (Platform.OS === 'web') {
        const domNode = ref && ref instanceof HTMLElement ? ref : (ref && ref._nativeTag ? document.querySelector(`[data-reactroot] [data-reactid="${ref._nativeTag}"]`) : null);
        const rect = ref && ref.getBoundingClientRect ? ref.getBoundingClientRect() : (domNode && domNode.getBoundingClientRect ? domNode.getBoundingClientRect() : null);
        if (rect) {
          const realIndex = index % DATA_LENGTH;
          onCardPress(item, { x: rect.left, y: rect.top, width: CARD_WIDTH, height: CARD_HEIGHT, scale: cardScale, carouselIndex: realIndex });
        }
      } else {
        UIManager.measure(findNodeHandle(ref), (x, y, w, h, pageX, pageY) => {
          const realIndex = index % DATA_LENGTH;
          onCardPress(item, { x: pageX, y: pageY, width: CARD_WIDTH, height: CARD_HEIGHT, scale: cardScale, carouselIndex: realIndex });
        });
      }
    }
  };

  if (hideAll) return null; // 所有卡片彻底消失

  const styles = StyleSheet.create({
    carouselWrap: {
      marginTop: 24,
      marginBottom: 32,
      minHeight: CARD_HEIGHT,
    },
    card: {
      height: CARD_HEIGHT,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 28,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.06,
      shadowRadius: 8,
      elevation: 2, // Android阴影
      padding: 18,
    },
    title: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#222',
      marginBottom: 12,
    },
    desc: {
      fontSize: 15,
      color: '#444',
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.carouselWrap}>
      <Animated.FlatList
        ref={flatListRef}
        data={loopData}
        keyExtractor={(_, i) => i + ''}
        horizontal
        showsHorizontalScrollIndicator={false}
        snapToInterval={CARD_WIDTH + CARD_SPACING}
        decelerationRate={Platform.OS === 'ios' ? 0 : 0.9} // iOS和Android不同的减速率
        bounces={false}
        contentContainerStyle={{ paddingHorizontal: SIDE_SPACE - 20 }}//调整两边卡片的间距
        onScroll={handleScroll}
        onMomentumScrollEnd={handleMomentumScrollEnd}
        scrollEventThrottle={16} // 60fps
        removeClippedSubviews={Platform.OS === 'android'} // Android性能优化
        maxToRenderPerBatch={3} // 限制渲染批次
        windowSize={5} // 优化内存使用
        renderItem={({ item, index }) => {
          // 使用scrollX实时计算是否为中心卡片
          const inputRange = [
            (index - 1) * (CARD_WIDTH + CARD_SPACING),
            index * (CARD_WIDTH + CARD_SPACING),
            (index + 1) * (CARD_WIDTH + CARD_SPACING),
          ];
          // lockedIndex时该卡片scale始终为1
          let scale;
          if (lockedIndex !== null && lockedIndex === index) {
            scale = 1;
          } else {
            scale = scrollX.interpolate({
              inputRange,
              outputRange: [0.9, 1, 0.9],
              extrapolate: 'clamp',
            });
          }
          // 新增：被隐藏卡片opacity为0
          let cardOpacity = 1;
          if (hiddenIndex !== null && hiddenIndex === index) {
            cardOpacity = 0;
          }
          const rotateY = scrollX.interpolate({
            inputRange,
            outputRange: ['-30deg', '0deg', '30deg'],
            extrapolate: 'clamp',
          });
          const rotateX = scrollX.interpolate({
            inputRange,
            outputRange: ['5deg', '0deg', '5deg'],
            extrapolate: 'clamp',
          });
          const opacity = scrollX.interpolate({
            inputRange,
            outputRange: [0.6, 1, 0.6],
            extrapolate: 'clamp',
          });

          // 简化中心判断逻辑
          const isCenter = Math.abs(index - centerIndex) < 0.5;
          
          return (
            <Animated.View
              ref={ref => (cardRefs.current[index] = ref)}
              style={{
                width: CARD_WIDTH,
                height: CARD_HEIGHT,
                marginHorizontal: CARD_SPACING / 2,
                transform: [
                  { perspective: 1500 },
                  { rotateY },
                  { rotateX },
                  { scale },
                ],
                opacity: cardOpacity,
              }}
            >
              {onCardPress ? (
                <TouchableOpacity 
                  activeOpacity={0.85} 
                  onPress={() => handlePress(item, index)} 
                  style={{ flex: 1 }}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }} // 增加点击区域
                >
                  <LinearGradient
                    colors={getCardGradient(index)}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                    style={styles.card}
                  >
                    <ThemedText style={styles.title}>{item.title}</ThemedText>
                    <ThemedText style={styles.desc}>{item.desc}</ThemedText>
                  </LinearGradient>
                </TouchableOpacity>
              ) : (
                <LinearGradient
                  colors={getCardGradient(index)}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 0, y: 1 }}
                  style={styles.card}
                >
                  <ThemedText style={styles.title}>{item.title}</ThemedText>
                  <ThemedText style={styles.desc}>{item.desc}</ThemedText>
                </LinearGradient>
              )}
            </Animated.View>
          );
        }}
      />
    </View>
  );
}