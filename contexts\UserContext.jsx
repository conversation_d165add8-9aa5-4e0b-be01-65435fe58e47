import { createContext, useContext, useEffect, useState } from "react";
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiServices from "../lib/apiServices"; // 使用新的统一API系统


export const UserContext = createContext();

export function UserProvider({ children }) {
  const [user, setUser] = useState(null);
  const [authChecked, setAuthChecked] = useState(false);
  const [profileSetupCompleted, setProfileSetupCompleted] = useState(false);
  const [recommendations, setRecommendations] = useState(null);

  async function register(email, password, checkPassword) {
    const result = await apiServices.auth.register(email, password, checkPassword);
    // 新用户注册后，为该用户设置画像完成状态为false
    const userProfileKey = `profileSetupCompleted_${email}`;
    await AsyncStorage.setItem(userProfileKey, "false");

    // 暂时不自动登录，等后端修复后再启用
    // return await login(email, password);

    return result;
  }

  async function login(userAccount, userPassword) {
    try {
      const loginResponse = await apiServices.auth.login(userAccount, userPassword);

      // 解析登录响应数据结构
      let userData;
      if (loginResponse.data && loginResponse.data.userAccount) {
        // 标准结构：{code: 0, message: 'ok', data: {userAccount, ...}}
        userData = loginResponse.data;
      } else if (loginResponse.userAccount) {
        // 直接返回用户数据
        userData = loginResponse;
      } else {
        throw new Error('登录响应数据格式错误');
      }

      setUser(userData);

      // ✅ 保存账号信息
      await AsyncStorage.setItem("userAccount", userData.userAccount);

      // ✅ 处理人物画像状态
      let isCompleted = false;
      const userProfileKey = `profileSetupCompleted_${userData.userAccount}`;

      if (userData.profileSetupCompleted !== undefined) {
        isCompleted = userData.profileSetupCompleted;
        await AsyncStorage.setItem(userProfileKey, isCompleted ? "true" : "false");
        console.log('从服务器获取画像完成状态:', isCompleted, '用户:', userData.userAccount);
      } else {
        const setupCompleted = await AsyncStorage.getItem(userProfileKey);
        isCompleted = setupCompleted === "true"; // 如果没有存储值，默认为false
        console.log('从本地存储获取画像完成状态:', isCompleted, '用户:', userData.userAccount);
        // 如果本地也没有存储，确保设置为false（新用户）
        if (setupCompleted === null) {
          await AsyncStorage.setItem(userProfileKey, "false");
          console.log('本地无画像状态记录，设置为false，用户:', userData.userAccount);
        }
      }

      setProfileSetupCompleted(isCompleted);
      console.log('登录成功，用户数据:', userData, '人物画像完成状态:', isCompleted);

      return { userData, profileSetupCompleted: isCompleted };
    } catch (error) {
      console.error('登录过程中发生错误:', error);
      throw error;
    }
  }


  // 游客登录函数
  async function guestLogin() {
    const guestUser = {
      id: 'guest_' + Date.now(),
      userAccount: 'guest',
      userName: '游客用户',
      userAvatar: null,
      userProfile: null,
      userRole: 'guest',
      profileSetupCompleted: true, // 游客不需要设置画像
      isGuest: true
    };

    setUser(guestUser);
    setProfileSetupCompleted(true);
    console.log('游客登录成功:', guestUser);
    return guestUser;
  }

  /**
   * 清理多余的 profileSetupCompleted 存储条目
   * 只保留最近的几个用户的数据，避免存储过多无用数据
   */
  async function cleanupProfileSetupStorage() {
    try {
      console.log('🧹 开始清理 profileSetupCompleted 存储数据');

      // 获取所有存储的键
      const allKeys = await AsyncStorage.getAllKeys();

      // 打印所有 keys 的日志信息
      console.log('📦 AsyncStorage 中的所有 keys:', allKeys);
      console.log(`📦 AsyncStorage 总共有 ${allKeys.length} 个键`);
      allKeys.forEach((key, index) => {
        console.log(`📦 [${index + 1}] ${key}`);
      });

      // 筛选出所有 profileSetupCompleted 相关的键
      const profileKeys = allKeys.filter(key => key.startsWith('profileSetupCompleted_'));

      console.log(`🧹 找到 ${profileKeys.length} 个 profileSetupCompleted 条目:`, profileKeys);

      if (profileKeys.length <= 3) {
        // 如果条目数量不多，不需要清理
        console.log('🧹 存储条目数量合理，无需清理');
        return;
      }

      // 获取当前用户账号（如果存在）
      const currentUserAccount = await AsyncStorage.getItem("userAccount");
      const currentUserKey = currentUserAccount ? `profileSetupCompleted_${currentUserAccount}` : null;

      // 获取所有条目的详细信息（包括最后修改时间的近似值）
      const keyDetails = await Promise.all(
        profileKeys.map(async (key) => {
          try {
            const value = await AsyncStorage.getItem(key);
            return { key, value, userAccount: key.replace('profileSetupCompleted_', '') };
          } catch (error) {
            console.warn(`🧹 无法读取键 ${key}:`, error);
            return null;
          }
        })
      );

      // 过滤掉无效的条目
      const validKeys = keyDetails.filter(item => item !== null);

      // 保留当前用户的键和最近的2个其他用户的键
      const keysToKeep = [];

      // 首先保留当前用户的键
      if (currentUserKey && validKeys.find(item => item.key === currentUserKey)) {
        keysToKeep.push(currentUserKey);
      }

      // 然后保留其他最近的2个键（排除当前用户的键）
      const otherKeys = validKeys
        .filter(item => item.key !== currentUserKey)
        .slice(-2) // 保留最后2个
        .map(item => item.key);

      keysToKeep.push(...otherKeys);

      // 找出需要删除的键
      const keysToDelete = profileKeys.filter(key => !keysToKeep.includes(key));

      if (keysToDelete.length > 0) {
        console.log(`🧹 将删除 ${keysToDelete.length} 个多余的条目:`, keysToDelete);

        // 批量删除多余的键
        await AsyncStorage.multiRemove(keysToDelete);

        console.log(`🧹 已成功清理 ${keysToDelete.length} 个多余的 profileSetupCompleted 条目`);
        console.log(`🧹 保留的条目:`, keysToKeep);
      } else {
        console.log('🧹 没有需要清理的条目');
      }

    } catch (error) {
      console.error('🧹 清理 profileSetupCompleted 存储时出错:', error);
    }
  }

  async function logout(currentRoute = null) {
    // 保存当前页面路由，用于下次登录时恢复
    if (currentRoute && !currentRoute.includes('profile')) {
      // 从非profile页面退出：保存当前页面，下次登录时恢复
      await AsyncStorage.setItem('lastVisitedRoute', currentRoute);
    } else if (currentRoute && currentRoute.includes('profile')) {
      // 从profile页面退出：根据用户类型设置不同的默认页面
      if (user?.isGuest) {
        // 游客从profile页面退出，设置默认恢复页面为index
        await AsyncStorage.setItem('lastVisitedRoute', '/(dashboard)/?tab=index');
      } else {
        // 正常用户从profile页面退出，设置默认恢复页面为plans
        await AsyncStorage.setItem('lastVisitedRoute', '/(dashboard)/plans?tab=plans');
      }
    }

    // 清理多余的 profileSetupCompleted 条目
    await cleanupProfileSetupStorage();

    setUser(null);
    // 重置当前的 profileSetupCompleted 状态，但不删除用户特定的存储数据
    // 下次登录时会从用户特定的存储中恢复正确的状态
    setProfileSetupCompleted(false);
    setRecommendations(null); // 清除推荐数据
    await AsyncStorage.removeItem("userAccount");
    await AsyncStorage.removeItem("auth_token"); // 清除 token

    // 清除游客会话数据
    await apiServices.auth.clearGuestSession();

    console.log('用户已退出登录，已清除游客会话和多余的存储数据');
  }

  async function updateProfileSetupStatus(completed) {
    setProfileSetupCompleted(completed);

    // 获取当前用户账号
    const userAccount = await AsyncStorage.getItem("userAccount");
    if (userAccount) {
      const userProfileKey = `profileSetupCompleted_${userAccount}`;
      await AsyncStorage.setItem(userProfileKey, completed ? "true" : "false");
    }
  }

  async function getInitialUserValue() {
    try {
      // 在应用启动时清理多余的存储数据
      await cleanupProfileSetupStorage();

      const userAccount = await AsyncStorage.getItem("userAccount");

      if (!userAccount) {

        // 检查是否有游客会话
        const guestSession = await apiServices.auth.getGuestSession();
        if (guestSession && !await apiServices.auth.isGuestSessionExpired()) {
          console.log('静默恢复游客会话（不跳转页面）:', guestSession);
          setUser(guestSession);
          setProfileSetupCompleted(true);

          // 恢复缓存的推荐数据
          const cachedData = await apiServices.auth.getCachedGuestData();
          if (cachedData?.recommendations) {
            setRecommendations(cachedData.recommendations);
            console.log('恢复缓存的推荐数据');
          }

          // 后台刷新数据，但不影响当前页面
          apiServices.auth.preloadGuestData().then((freshData) => {
            if (freshData.recommendations && freshData.recommendations.length > 0) {
              setRecommendations(freshData.recommendations);
              console.log('后台更新推荐数据完成');
            }
          }).catch((error) => {
            console.warn('后台更新数据失败:', error);
          });

          setAuthChecked(true);
          return;
        }

        console.log('没有有效的游客会话，跳过自动登录');
        setAuthChecked(true);
        return;
      }

      // 先从本地存储恢复基本用户信息，保证登录状态
      const basicUserData = {
        id: Date.now(), // 使用时间戳作为临时ID
        userAccount: userAccount,
        userName: userAccount, // 使用账号作为默认用户名
        userAvatar: null,
        userProfile: null,
        userRole: 'user',
        isGuest: false, // 明确设置为非游客用户
        profileSetupCompleted: false
      };

      // 检查用户是否已完成人物画像设置
      const userProfileKey = `profileSetupCompleted_${userAccount}`;
      const setupCompleted = await AsyncStorage.getItem(userProfileKey);
      const isCompleted = setupCompleted === "true"; // 如果没有存储值，默认为false
      basicUserData.profileSetupCompleted = isCompleted;

      // 如果本地没有存储画像状态，设置为false
      if (setupCompleted === null) {
        await AsyncStorage.setItem(userProfileKey, "false");
      }

      // 先设置基本用户信息，确保登录状态
      setUser(basicUserData);
      setProfileSetupCompleted(isCompleted);

      // 使用轻量级验证检查用户身份（只获取 id 和 userAccount）
      try {
        // 先检查是否有 token
        const token = await AsyncStorage.getItem("auth_token");
        console.log('检查存储的 token:', token ? `存在 (${token.substring(0, 20)}...)` : '不存在');

        // 同时检查所有存储的 keys 来调试
        if (!token) {
          // 没有找到 token，跳过服务器验证，保持本地登录状态
        } else {
          try {
            // 添加超时处理，避免API调用卡住
            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('API验证超时')), 5000)
            );
            const basicValidation = await Promise.race([
              apiServices.auth.verifyUser(),
              timeoutPromise
            ]);

          if (basicValidation && (basicValidation.id || basicValidation.userAccount)) {
            // ✅ 验证成功，更新必要信息
            let hasUpdates = false;

            if (basicValidation.id && basicValidation.id !== basicUserData.id) {
              const updatedUserData = {
                ...basicUserData,
                id: basicValidation.id
              };
              setUser(updatedUserData);
              console.log('🔄 更新用户ID为服务器数据:', updatedUserData.id);
              hasUpdates = true;
            }

            // 验证用户账号是否匹配
            if (basicValidation.userAccount && basicValidation.userAccount !== userAccount) {
              console.warn('⚠️ 服务器返回的用户账号与本地存储不匹配');
              console.warn('本地:', userAccount, '服务器:', basicValidation.userAccount);
            }

            console.log(`✅ 轻量级用户验证成功${hasUpdates ? '，已更新用户信息' : '，信息一致'}`);
          } else {
            console.log('❌ 轻量级用户验证失败，但保持本地登录状态');
            console.log('💡 用户可继续使用应用，部分功能可能受限');
          }
          } catch (verifyError) {
            console.log('🚨 用户验证API调用失败:', verifyError.message);
            console.log('💡 继续使用本地用户数据');
          }
        }
      } catch (validationError) {
        console.log('轻量级用户验证出错，保持本地登录状态:', validationError.message);
        // 验证出错但不影响用户体验，继续使用本地存储的信息
      }

    } catch (e) {
      console.error("❌ 恢复用户状态失败", e);
      // 即使出错也不清除本地存储，尝试保持登录状态
      const userAccount = await AsyncStorage.getItem("userAccount");
      if (userAccount) {
        const fallbackUserData = {
          id: Date.now(),
          userAccount: userAccount,
          userName: userAccount,
          userAvatar: null,
          userProfile: null,
          userRole: 'user',
          isGuest: false, // 明确设置为非游客用户
          profileSetupCompleted: false
        };
        setUser(fallbackUserData);
      } else {
        setUser(null);
      }
    }
    setAuthChecked(true);
  }

  useEffect(() => {
    getInitialUserValue();
  }, []);

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
        login,
        guestLogin,
        logout,
        register,
        authChecked,
        setAuthChecked, // 暴露setAuthChecked用于紧急修复
        profileSetupCompleted,
        setProfileSetupCompleted,
        updateProfileSetupStatus,
        recommendations,
        setRecommendations,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

// Hook for using recommendation functionality
export const useRecommendation = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useRecommendation must be used within a UserProvider');
  }
  return {
    recommendations: context.recommendations,
    setRecommendations: context.setRecommendations,
  };
};
