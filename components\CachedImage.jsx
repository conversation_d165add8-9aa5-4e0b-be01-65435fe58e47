// components/CachedImage.jsx - 集成缓存管理的高性能图片组件
import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { Image, View, Platform, Animated } from 'react-native';
import { getSafeImageUrl } from '../lib/imageUtils';
import imageCacheManager from '../lib/imageCacheManager';
import ThemedText from './ThemedText';

const CachedImage = ({ 
  source, 
  style, 
  placeholder = 'https://via.placeholder.com/300x200?text=Loading...',
  showDebugInfo = false,
  onError,
  onLoad,
  fadeInDuration = 200,  // 淡入动画时长
  enablePreload = true,  // 是否启用预加载
  ...props 
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [imageLoaded, setImageLoaded] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const backgroundFadeAnim = useRef(new Animated.Value(1)).current;
  const mountedRef = useRef(true);

  // 使用useMemo缓存图片源处理结果，避免重复计算
  const processedImageUrl = useMemo(() => {
    if (!source) return placeholder;
    
    if (typeof source === 'string') {
      return getSafeImageUrl(source, placeholder);
    }
    
    if (source.uri) {
      return getSafeImageUrl(source.uri, placeholder);
    }
    
    return placeholder;
  }, [source, placeholder]);

  const imageSource = useMemo(() => {
    // 如果是本地图片资源，直接返回
    if (source && typeof source === 'object' && !source.uri) {
      return source;
    }
    return { uri: processedImageUrl };
  }, [source, processedImageUrl]);

  const placeholderSource = useMemo(() => ({ uri: placeholder }), [placeholder]);

  // 组件卸载时标记
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 检查缓存并预加载
  useEffect(() => {
    if (!processedImageUrl || processedImageUrl === placeholder) return;

    // 检查缓存状态
    const cachedStatus = imageCacheManager.getCachedImageStatus(processedImageUrl);
    
    if (cachedStatus) {
      // 从缓存中获取状态
      if (cachedStatus.loaded) {
        setImageLoaded(true);
        setImageError(false);
        setIsLoading(false);
        // 直接显示图片，无需动画
        fadeAnim.setValue(1);
        backgroundFadeAnim.setValue(0);
      } else if (cachedStatus.error) {
        setImageError(true);
        setIsLoading(false);
        setImageLoaded(false);
      }
    } else if (enablePreload && !imageCacheManager.isLoading(processedImageUrl)) {
      // 预加载图片
      imageCacheManager.preloadImage(processedImageUrl).then((success) => {
        if (!mountedRef.current) return;
        
        if (success) {
          setImageLoaded(true);
          setImageError(false);
          setIsLoading(false);
        } else {
          setImageError(true);
          setIsLoading(false);
          setImageLoaded(false);
        }
      });
    }
  }, [processedImageUrl, placeholder, enablePreload, fadeAnim, backgroundFadeAnim]);

  // 重置状态当源改变时
  useEffect(() => {
    if (source) {
      const cachedStatus = imageCacheManager.getCachedImageStatus(processedImageUrl);
      if (!cachedStatus || !cachedStatus.loaded) {
        setImageLoaded(false);
        setImageError(false);
        setIsLoading(true);
        fadeAnim.setValue(0);
        backgroundFadeAnim.setValue(1);
      }
    }
  }, [source, processedImageUrl, fadeAnim, backgroundFadeAnim]);

  // 使用useCallback优化事件处理函数
  const handleError = useCallback((error) => {
    if (!mountedRef.current) return;

    // console.error(`[${Platform.OS}] 图片加载失败:`, {
    //   originalSource: source,
    //   processedUrl: processedImageUrl,
    //   error: error.nativeEvent?.error
    // });

    setImageError(true);
    setIsLoading(false);
    setImageLoaded(false);
    
    // 缓存失败状态
    imageCacheManager.setCachedImageStatus(processedImageUrl, { 
      loaded: false, 
      error: true 
    });
    
    if (onError) onError(error);
  }, [source, processedImageUrl, onError]);

  const handleLoad = useCallback((event) => {
    if (!mountedRef.current) return;
    
    setIsLoading(false);
    setImageError(false);
    setImageLoaded(true);
    
    // 缓存成功状态
    imageCacheManager.setCachedImageStatus(processedImageUrl, { 
      loaded: true, 
      error: false 
    });
    
    // 启动淡入动画
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: fadeInDuration,
        useNativeDriver: Platform.OS !== 'web',
      }),
      Animated.timing(backgroundFadeAnim, {
        toValue: 0,
        duration: fadeInDuration,
        useNativeDriver: Platform.OS !== 'web',
      })
    ]).start();
    
    if (onLoad) onLoad(event);

    // if (showDebugInfo) {
    //   console.log(`[${Platform.OS}] 图片加载成功:`, {
    //     originalSource: source,
    //     processedUrl: processedImageUrl,
    //     cached: imageCacheManager.isCached(processedImageUrl)
    //   });
    // }
  }, [onLoad, showDebugInfo, source, processedImageUrl, fadeAnim, backgroundFadeAnim, fadeInDuration]);

  const handleLoadStart = useCallback(() => {
    if (!mountedRef.current) return;
    setIsLoading(true);
    setImageError(false);
    setImageLoaded(false);
  }, []);

  // 如果图片加载失败，显示占位符
  if (imageError) {
    return (
      <View style={[style, { 
        backgroundColor: '#f8f8f8', 
        justifyContent: 'center', 
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderStyle: 'dashed'
      }]}>
        <ThemedText style={{ 
          fontSize: 12, 
          color: '#999', 
          textAlign: 'center',
          padding: 10
        }}>
          图片加载失败
        </ThemedText>
        {showDebugInfo && (
          <ThemedText style={{ 
            fontSize: 10, 
            color: '#666', 
            textAlign: 'center',
            marginTop: 5
          }}>
            {processedImageUrl}
          </ThemedText>
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      {/* 背景占位符图片 */}
      <Animated.View style={[
        { 
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: backgroundFadeAnim
        }
      ]}>
        <Image
          source={placeholderSource}
          style={[style, { backgroundColor: '#f5f5f5' }]}
          blurRadius={1}
        />
      </Animated.View>

      {/* 实际图片 */}
      <Animated.View style={[
        { 
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: fadeAnim
        }
      ]}>
        <Image
          {...props}
          source={imageSource}
          style={style}
          onError={handleError}
          onLoad={handleLoad}
          onLoadStart={handleLoadStart}
          // 添加缓存策略
          cache="force-cache"
        />
      </Animated.View>

      {/* 调试信息 */}
      {showDebugInfo && (
        <View style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'rgba(0,0,0,0.7)',
          padding: 5
        }}>
          <ThemedText style={{ fontSize: 9, color: 'white' }}>
            状态: {isLoading ? '加载中' : imageLoaded ? '已加载' : '未加载'} | 
            缓存: {imageCacheManager.isCached(processedImageUrl) ? '命中' : '未命中'}
          </ThemedText>
          <ThemedText style={{ fontSize: 8, color: '#ccc' }}>
            {processedImageUrl?.substring(0, 50)}...
          </ThemedText>
        </View>
      )}
    </View>
  );
};

export default CachedImage;
