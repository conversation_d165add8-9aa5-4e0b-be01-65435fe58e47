import React, { useState, useEffect } from 'react';
import { View, ScrollView, TouchableOpacity, StyleSheet, RefreshControl } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import ThemedText from '../../components/ThemedText';
import ThemedView from '../../components/ThemedView';
import PostCard from '../../components/PostCard';
import { postApi, authApi } from '../../lib/apiServices';

export default function PostDetail() {
  const router = useRouter();
  const { postId } = useLocalSearchParams();
  
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // 格式化时间显示
  const formatTime = (createTime) => {
    if (!createTime) return '未知时间';
    
    const createDate = new Date(createTime);
    const now = new Date();
    const diffMs = now.getTime() - createDate.getTime();
    
    // 如果时间差为负数（未来时间），显示刚刚
    if (diffMs < 0) return '刚刚';
    
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);
    
    if (diffSeconds < 60) return '刚刚';
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffWeeks < 4) return `${diffWeeks}周前`;
    if (diffMonths < 12) return `${diffMonths}个月前`;
    return `${diffYears}年前`;
  };

  // 获取帖子详情
  const fetchPostDetail = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 获取帖子详情:', { postId });

      // 1. 获取帖子详情
      const response = await postApi.getById(postId);
      const postData = response.data;

      if (!postData) {
        throw new Error('帖子不存在');
      }

      console.log('📄 获取到帖子数据:', postData);

      // 2. 获取用户信息
      let userInfo = null;
      if (postData.userId) {
        try {
          userInfo = await authApi.getUserById(postData.userId);
          console.log('👤 获取到用户信息:', userInfo);
        } catch (userError) {
          console.warn('获取用户信息失败:', userError);
        }
      }

      // 3. 格式化帖子数据（与community.jsx保持一致）
      const formattedPost = {
        id: postData.id,
        title: postData.title,
        content: postData.content,
        imageUrls: postData.imageUrls ? postData.imageUrls.split(',').filter(Boolean) : [],
        tag: postData.tag,
        likeCount: postData.likeCount || 0,
        commentCount: postData.commentCount || 0,
        viewCount: postData.viewCount || 0,
        isHidden: postData.isHidden || false,
        createTime: postData.createTime,
        time: formatTime(postData.createTime),
        // 用户信息：优先使用获取到的用户信息，否则使用userId
        userId: postData.userId,
        user: userInfo?.userAccount || userInfo?.userName || postData.userId || 'Unknown',
        userAvatar: userInfo?.userAvatar || null,
        userInfo: userInfo || null,
        // 为了兼容PostCard组件，添加一些派生字段
        likes: postData.likeCount || 0,
        comments: postData.commentCount || 0,
        hasMedia: postData.imageUrls && postData.imageUrls.trim().length > 0,
      };

      setPost(formattedPost);
      console.log('✅ 帖子详情加载完成:', formattedPost);

    } catch (err) {
      console.error('❌ 获取帖子详情失败:', err);
      setError(err.message || '获取帖子详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchPostDetail();
    setRefreshing(false);
  };

  // 处理点赞
  const handleLikeToggle = (postId, isLiked) => {
    console.log('点赞状态变化:', { postId, isLiked });
    // 更新本地状态
    setPost(prev => ({
      ...prev,
      likes: isLiked ? prev.likes + 1 : prev.likes - 1,
      likeCount: isLiked ? prev.likeCount + 1 : prev.likeCount - 1,
    }));
  };

  // 处理评论
  const handleCommentPress = () => {
    console.log('点击评论按钮');
    // TODO: 跳转到评论页面或显示评论弹窗
  };

  // 处理转发
  const handleForwardPress = () => {
    console.log('点击转发按钮');
    // TODO: 实现转发功能
  };

  useEffect(() => {
    if (postId) {
      fetchPostDetail();
    }
  }, [postId]);

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ThemedText style={styles.backText}>← 返回</ThemedText>
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>帖子详情</ThemedText>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ThemedText style={styles.loadingText}>加载中...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ThemedText style={styles.backText}>← 返回</ThemedText>
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>帖子详情</ThemedText>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>{error}</ThemedText>
          <TouchableOpacity style={styles.retryButton} onPress={fetchPostDetail}>
            <ThemedText style={styles.retryButtonText}>重试</ThemedText>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* 顶部导航栏 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ThemedText style={styles.backText}>← 返回</ThemedText>
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>帖子详情</ThemedText>
        <View style={styles.placeholder} />
      </View>

      {/* 帖子内容 */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {post && (
          <PostCard
            post={post}
            onLikeToggle={handleLikeToggle}
            onCommentPress={handleCommentPress}
            onForwardPress={handleForwardPress}
          />
        )}
        
        {/* 评论区域 */}
        <View style={styles.commentsSection}>
          <ThemedText style={styles.commentsTitle}>评论 ({post?.comments || 0})</ThemedText>
          <View style={styles.commentsPlaceholder}>
            <ThemedText style={styles.commentsPlaceholderText}>
              评论功能开发中...
            </ThemedText>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: 'rgba(255, 248, 243, 0.95)',
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    padding: 8,
  },
  backText: {
    fontSize: 16,
    color: '#007AFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 50,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff4444',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  commentsSection: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  commentsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  commentsPlaceholder: {
    padding: 20,
    alignItems: 'center',
  },
  commentsPlaceholderText: {
    color: '#999',
    fontSize: 14,
  },
});
