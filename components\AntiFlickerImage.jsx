// components/AntiFlickerImage.jsx - 专门防止闪屏的图片组件
import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { Image, View, Platform, Animated } from 'react-native';
import { getSafeImageUrl } from '../lib/imageUtils';
import ThemedText from './ThemedText';

const AntiFlickerImage = ({ 
  source, 
  style, 
  placeholder = 'https://via.placeholder.com/300x200?text=Loading...',
  showDebugInfo = false,
  onError,
  onLoad,
  fadeInDuration = 300,  // 淡入动画时长
  ...props 
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [imageLoaded, setImageLoaded] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const backgroundFadeAnim = useRef(new Animated.Value(1)).current;

  // 使用useMemo缓存图片源处理结果，避免重复计算
  const imageSource = useMemo(() => {
    if (!source) return { uri: placeholder };
    
    if (typeof source === 'string') {
      const transformedUrl = getSafeImageUrl(source, placeholder);
      return { uri: transformedUrl };
    }
    
    if (source.uri) {
      const transformedUrl = getSafeImageUrl(source.uri, placeholder);
      return { ...source, uri: transformedUrl };
    }
    
    // 如果是本地图片资源，直接返回
    return source;
  }, [source, placeholder]);

  // 占位符图片源
  const placeholderSource = useMemo(() => ({ uri: placeholder }), [placeholder]);

  // 重置动画状态
  useEffect(() => {
    if (source) {
      setImageLoaded(false);
      setImageError(false);
      setIsLoading(true);
      fadeAnim.setValue(0);
      backgroundFadeAnim.setValue(1);
    }
  }, [source, fadeAnim, backgroundFadeAnim]);

  // 使用useCallback优化事件处理函数，避免重复创建
  const handleError = useCallback((error) => {
    console.error(`[${Platform.OS}] 图片加载失败:`, {
      originalSource: source,
      transformedSource: imageSource,
      error: error.nativeEvent?.error
    });
    setImageError(true);
    setIsLoading(false);
    setImageLoaded(false);
    if (onError) onError(error);
  }, [source, imageSource, onError]);

  const handleLoad = useCallback((event) => {
    setIsLoading(false);
    setImageError(false);
    setImageLoaded(true);
    
    // 启动淡入动画
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: fadeInDuration,
        useNativeDriver: Platform.OS !== 'web',
      }),
      Animated.timing(backgroundFadeAnim, {
        toValue: 0,
        duration: fadeInDuration,
        useNativeDriver: Platform.OS !== 'web',
      })
    ]).start();
    
    if (onLoad) onLoad(event);
    
    if (showDebugInfo) {
      console.log(`[${Platform.OS}] 图片加载成功:`, {
        originalSource: source,
        transformedSource: imageSource
      });
    }
  }, [onLoad, showDebugInfo, source, imageSource, fadeAnim, backgroundFadeAnim, fadeInDuration]);

  const handleLoadStart = useCallback(() => {
    setIsLoading(true);
    setImageError(false);
    setImageLoaded(false);
  }, []);

  // 如果图片加载失败，显示占位符
  if (imageError) {
    return (
      <View style={[style, { 
        backgroundColor: '#f8f8f8', 
        justifyContent: 'center', 
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderStyle: 'dashed'
      }]}>
        <ThemedText style={{ 
          fontSize: 12, 
          color: '#999', 
          textAlign: 'center',
          padding: 10
        }}>
          图片加载失败
        </ThemedText>
        {showDebugInfo && (
          <ThemedText style={{ 
            fontSize: 10, 
            color: '#666', 
            textAlign: 'center',
            marginTop: 5
          }}>
            {typeof source === 'string' ? source : source?.uri}
          </ThemedText>
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      {/* 背景占位符图片 */}
      <Animated.View style={[
        { 
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: backgroundFadeAnim
        }
      ]}>
        <Image
          source={placeholderSource}
          style={[style, { backgroundColor: '#f5f5f5' }]}
          blurRadius={2}  // 添加模糊效果，让占位符更柔和
        />
      </Animated.View>

      {/* 实际图片 */}
      <Animated.View style={[
        { 
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: fadeAnim
        }
      ]}>
        <Image
          {...props}
          source={imageSource}
          style={style}
          onError={handleError}
          onLoad={handleLoad}
          onLoadStart={handleLoadStart}
          // 添加缓存策略，减少重复加载
          cache="force-cache"
        />
      </Animated.View>

      {/* 调试信息 */}
      {showDebugInfo && (
        <View style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'rgba(0,0,0,0.7)',
          padding: 5
        }}>
          <ThemedText style={{ fontSize: 9, color: 'white' }}>
            状态: {isLoading ? '加载中' : imageLoaded ? '已加载' : '未加载'}
          </ThemedText>
          <ThemedText style={{ fontSize: 8, color: '#ccc' }}>
            {typeof source === 'string' ? source : source?.uri || 'Unknown'}
          </ThemedText>
        </View>
      )}
    </View>
  );
};

export default AntiFlickerImage;
