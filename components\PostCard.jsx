import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Animated, Easing, Modal, Alert } from 'react-native';
import ThemedText from "./ThemedText";
import { postApi } from '../lib/apiServices';

// 导入头像图片
const avatarJessica = require('../assets/Community_image/AvatarOne.png');
const avatarKin = require('../assets/Community_image/AvatarTwo.png');
const avatarCaaary = require('../assets/Community_image/AvatarThree.png');

// 导入图标
const likeIcon = require('../assets/Like33.png');
const commentIcon = require('../assets/Comments.png');
const forwardIcon = require('../assets/Many.png');
const menuIcon = require('../assets/FrameThree.png'); // 使用现有的三个点图标

// 导入Kin的图片
const kinPictures = [
  require('../assets/Community_image/PictureTwo.png'),
  require('../assets/Community_image/PictureThree.png'),
  require('../assets/Community_image/PictureFour.png'),
  require('../assets/Community_image/PictureFive.png'),
  require('../assets/Community_image/PictureSix.png'),
  require('../assets/Community_image/PhotosOne.png'),
];

// 带动画的点赞组件
const AnimatedLikeButton = ({ post, onLikeToggle }) => {
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(post.likes);
  const [isAnimating, setIsAnimating] = useState(false);

  // 动画值
  const shakeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const particleAnims = useRef(Array.from({ length: 6 }, () => ({
    translateX: new Animated.Value(0),
    translateY: new Animated.Value(0),
    opacity: new Animated.Value(0),
    scale: new Animated.Value(1)
  }))).current;

  // 点赞动画
  const animateLike = () => {
    if (isAnimating) return;
    setIsAnimating(true);

    // 抖动动画
    Animated.sequence([
      Animated.timing(shakeAnim, { toValue: 10, duration: 50, useNativeDriver: Platform.OS !== 'web' }),
      Animated.timing(shakeAnim, { toValue: -10, duration: 50, useNativeDriver: Platform.OS !== 'web' }),
      Animated.timing(shakeAnim, { toValue: 5, duration: 50, useNativeDriver: Platform.OS !== 'web' }),
      Animated.timing(shakeAnim, { toValue: 0, duration: 50, useNativeDriver: Platform.OS !== 'web' })
    ]).start();

    // 缩放动画
    Animated.sequence([
      Animated.timing(scaleAnim, { toValue: 1.3, duration: 100, useNativeDriver: Platform.OS !== 'web' }),
      Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: Platform.OS !== 'web' })
    ]).start();

    // 粒子散发动画
    const particleAnimations = particleAnims.map((particle, index) => {
      const angle = (index * 60) * Math.PI / 180;
      const distance = 30;
      const targetX = Math.cos(angle) * distance;
      const targetY = Math.sin(angle) * distance;

      return Animated.parallel([
        Animated.timing(particle.translateX, { toValue: targetX, duration: 400, easing: Easing.out(Easing.quad), useNativeDriver: Platform.OS !== 'web' }),
        Animated.timing(particle.translateY, { toValue: targetY, duration: 400, easing: Easing.out(Easing.quad), useNativeDriver: Platform.OS !== 'web' }),
        Animated.sequence([
          Animated.timing(particle.opacity, { toValue: 1, duration: 100, useNativeDriver: Platform.OS !== 'web' }),
          Animated.timing(particle.opacity, { toValue: 0, duration: 300, useNativeDriver: Platform.OS !== 'web' })
        ]),
        Animated.sequence([
          Animated.timing(particle.scale, { toValue: 1.2, duration: 100, useNativeDriver: Platform.OS !== 'web' }),
          Animated.timing(particle.scale, { toValue: 0, duration: 300, useNativeDriver: Platform.OS !== 'web' })
        ])
      ]);
    });

    Animated.parallel(particleAnimations).start(() => {
      particleAnims.forEach(particle => {
        particle.translateX.setValue(0);
        particle.translateY.setValue(0);
        particle.opacity.setValue(0);
        particle.scale.setValue(1);
      });
      setIsAnimating(false);
    });
  };

  // 处理点赞
  const handleLike = async () => {
    if (isAnimating) return;

    const originalIsLiked = isLiked;
    const originalLikeCount = likeCount;

    console.log('🔄 开始点赞操作:', {
      postId: post.id,
      currentIsLiked: isLiked,
      currentLikeCount: likeCount
    });

    try {
      if (!isLiked) {
        console.log('👍 执行点赞...');
        setIsAnimating(true);
        animateLike();
        setIsLiked(true);
        setLikeCount(prev => prev + 1);

        const result = await postApi.like(post.id);
        console.log('✅ 点赞成功:', result);
      } else {
        console.log('👎 执行取消点赞...');
        setIsLiked(false);
        setLikeCount(prev => prev - 1);

        const result = await postApi.unlike(post.id);
        console.log('✅ 取消点赞成功:', result);
      }

      onLikeToggle && onLikeToggle(post.id, !originalIsLiked);

    } catch (error) {
      console.error('❌ 点赞操作失败:', {
        error: error.message,
        postId: post.id,
        isLiked: originalIsLiked,
        stack: error.stack
      });

      // 恢复原始状态
      setIsLiked(originalIsLiked);
      setLikeCount(originalLikeCount);

      // 显示用户友好的错误信息
      alert(`点赞失败: ${error.message}`);
    } finally {
      setIsAnimating(false);
    }
  };

  return (
    <TouchableOpacity style={styles.likeContainer} onPress={handleLike}>
      {/* 粒子效果 */}
      {particleAnims.map((particle, index) => (
        <Animated.View
          key={index}
          style={[
            styles.particle,
            {
              transform: [
                { translateX: particle.translateX },
                { translateY: particle.translateY },
                { scale: particle.scale }
              ],
              opacity: particle.opacity
            }
          ]}
        />
      ))}

      {/* 点赞按钮 */}
      <Animated.View
        style={[
          styles.likeButton,
          {
            transform: [
              { translateX: shakeAnim },
              { scale: scaleAnim }
            ]
          }
        ]}
      >
        <Image
          source={likeIcon}
          style={[
            styles.likeIcon,
            isLiked && styles.likeIconActive
          ]}
        />
      </Animated.View>

      <ThemedText style={[
        styles.likeCount,
        isLiked && styles.likeCountActive
      ]}>
        {likeCount}
      </ThemedText>
    </TouchableOpacity>
  );
};

// 帖子菜单组件
const PostMenu = ({ visible, onClose, post, onHidePost }) => {
  const handleHidePost = async () => {
    try {
      Alert.alert(
        '确认操作',
        post.isHidden ? '确定要显示这个帖子吗？' : '确定要隐藏这个帖子吗？',
        [
          {
            text: '取消',
            style: 'cancel',
          },
          {
            text: '确定',
            onPress: async () => {
              console.log('🔄 切换帖子隐藏状态:', { postId: post.id, currentHidden: post.isHidden });
              await onHidePost(post.id, !post.isHidden);
              onClose();
            },
          },
        ]
      );
    } catch (error) {
      console.error('❌ 操作失败:', error);
      Alert.alert('错误', '操作失败，请重试');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View style={styles.menuContainer}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={handleHidePost}
          >
            <ThemedText style={styles.menuItemText}>
              {post.isHidden ? '显示帖子' : '隐藏帖子'}
            </ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.menuItem, styles.cancelMenuItem]}
            onPress={onClose}
          >
            <ThemedText style={[styles.menuItemText, styles.cancelMenuItemText]}>
              取消
            </ThemedText>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

// 帖子卡片组件
const PostCard = ({ post, onCommentPress, onForwardPress, onLikeToggle, onPostPress, onHidePost }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showMenu, setShowMenu] = useState(false);

  // 实时更新时间显示
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // 每分钟更新一次

    return () => clearInterval(timer);
  }, []);

  // 格式化时间显示
  const formatTimeFromNow = (createTime) => {
    if (!createTime) return '未知时间';

    const createDate = new Date(createTime);
    const now = currentTime;
    const diffMs = now.getTime() - createDate.getTime();

    // 如果时间差为负数（未来时间），显示刚刚
    if (diffMs < 0) return '刚刚';

    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    if (diffSeconds < 60) return '刚刚';
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffWeeks < 4) return `${diffWeeks}周前`;
    if (diffMonths < 12) return `${diffMonths}个月前`;
    return `${diffYears}年前`;
  };
  // 获取用户头像
  const getUserAvatar = (post) => {
    // 优先使用用户的真实头像
    if (post.userAvatar) {
      return (
        <Image
          source={{ uri: post.userAvatar }}
          style={styles.avatar}
          defaultSource={require('../assets/Community_image/AvatarOne.png')}
        />
      );
    }

    // 如果没有头像，根据用户名使用默认头像
    const user = post.user || post.userId;
    switch (user) {
      case 'Jessica':
        return <Image source={avatarJessica} style={styles.avatar} />;
      case 'Kin':
        return <Image source={avatarKin} style={styles.avatar} />;
      case 'Caaary':
        return <Image source={avatarCaaary} style={styles.avatar} />;
      default:
        // 根据用户ID生成颜色
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
        const colorIndex = (user || '').length % colors.length;
        return (
          <View style={[styles.avatar, { backgroundColor: colors[colorIndex] }]}>
            <ThemedText style={styles.avatarText}>
              {(user || 'U').charAt(0).toUpperCase()}
            </ThemedText>
          </View>
        );
    }
  };

  // 渲染媒体内容
  const renderMedia = () => {
    if (!post.hasMedia || !post.imageUrls || post.imageUrls.length === 0) return null;

    const images = post.imageUrls;
    const imageCount = images.length;

    return (
      <View style={styles.mediaContainer}>
        {imageCount === 1 ? (
          // 单张图片
          <Image
            source={{ uri: images[0] }}
            style={styles.singleMedia}
            onError={() => {
              console.log('图片加载失败:', images[0]);
              // 可以设置默认图片
            }}
          />
        ) : (
          // 多张图片网格布局
          <View style={[styles.gridMedia, { flexDirection: 'row', flexWrap: 'wrap' }]}>
            {images.slice(0, 9).map((imageUrl, idx) => (
              <Image
                key={idx}
                source={{ uri: imageUrl }}
                style={{
                  width: imageCount === 2 ? 120 : 80,
                  height: imageCount === 2 ? 120 : 80,
                  borderRadius: 8,
                  margin: 2
                }}
                onError={() => {
                  console.log('图片加载失败:', imageUrl);
                }}
              />
            ))}
            {imageCount > 9 && (
              <View style={{
                width: 80,
                height: 80,
                borderRadius: 8,
                margin: 2,
                backgroundColor: 'rgba(0,0,0,0.5)',
                justifyContent: 'center',
                alignItems: 'center'
              }}>
                <ThemedText style={{ color: 'white', fontSize: 12 }}>
                  +{imageCount - 9}
                </ThemedText>
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={styles.post}
      onPress={() => onPostPress && onPostPress(post)}
      activeOpacity={0.95}
    >
      {/* 用户信息和时间 */}
      <View style={styles.postHeader}>
        <View style={styles.userInfo}>
          {getUserAvatar(post)}
          <View style={styles.userDetails}>
            <ThemedText style={styles.username}>{post.user || post.userId || 'Unknown'}</ThemedText>
          </View>
        </View>
        {/* 时间显示在右上角 - 使用绝对定位 */}
        <View style={styles.timeContainer}>
          <ThemedText style={styles.time}>{formatTimeFromNow(post.createTime)}</ThemedText>
        </View>
      </View>

      {/* 帖子内容 */}
      <ThemedText style={styles.postContent}>{post.content}</ThemedText>

      {/* 媒体内容 */}
      {renderMedia()}

      {/* 互动按钮 */}
      <View style={styles.interactions}>
        {/* 点赞按钮 */}
        <View style={styles.interactionBtn}>
          <AnimatedLikeButton
            post={post}
            onLikeToggle={onLikeToggle}
          />
        </View>

        {/* 评论按钮 */}
        <TouchableOpacity
          style={styles.interactionBtn}
          onPress={() => onCommentPress && onCommentPress(post)}
        >
          <Image source={commentIcon} style={{ width: 20, height: 20, marginRight: 2 }} />
          <ThemedText style={styles.interactionCount}>{post.comments}</ThemedText>
        </TouchableOpacity>

        {/* 占位符 */}
        <View style={{ flex: 1 }} />

        {/* 菜单按钮 */}
        <TouchableOpacity
          style={styles.interactionBtnRight}
          onPress={(e) => {
            e.stopPropagation(); // 阻止事件冒泡
            setShowMenu(true);
          }}
        >
          <ThemedText style={styles.menuButtonText}>⋯</ThemedText>
        </TouchableOpacity>
      </View>

      {/* 帖子菜单 */}
      <PostMenu
        visible={showMenu}
        onClose={() => {
          console.log('🔘 菜单关闭');
          setShowMenu(false);
        }}
        post={post}
        onHidePost={onHidePost}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  post: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    marginHorizontal: 20, // 左右内边距20
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  postHeader: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingRight: 80, // 为时间留出空间
  },
  userInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  userDetails: {
    flex: 1,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  timeContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  time: {
    fontSize: 12,
    color: '#999',
    fontWeight: '500',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
  },
  postContent: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 12,
  },
  mediaContainer: {
    marginBottom: 12,
  },
  singleMedia: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  gridMedia: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  gridItem: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    margin: 2,
  },
  interactions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  interactionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  interactionBtnRight: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8, // 增加点击区域
    minWidth: 40, // 最小宽度
    minHeight: 40, // 最小高度
    justifyContent: 'center',
  },
  interactionCount: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  // 点赞动画样式
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  likeButton: {
    marginRight: 4,
  },
  likeIcon: {
    width: 20,
    height: 20,
    tintColor: '#666',
  },
  likeIconActive: {
    tintColor: '#FF69B4',
  },
  likeCount: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  likeCountActive: {
    color: '#FF69B4',
    fontWeight: 'bold',
  },
  particle: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FF69B4',
    left: 10,
    top: 10,
  },
  // 菜单按钮样式
  menuButtonText: {
    fontSize: 20,
    color: '#666',
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: 24,
  },
  // 菜单弹窗样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    minWidth: 200,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  },
  menuItem: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  cancelMenuItem: {
    borderBottomWidth: 0,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    marginTop: 8,
  },
  menuItemText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
  cancelMenuItemText: {
    color: '#999',
  },
});

export default PostCard;
