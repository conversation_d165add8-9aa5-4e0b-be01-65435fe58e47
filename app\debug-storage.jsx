import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import StorageCleanupTool from '../components/StorageCleanupTool';
import StorageTestHelper from '../components/StorageTestHelper';
import LoginStatusChecker from '../components/LoginStatusChecker';
import GradientGlassButton from '../components/GradientGlassButton';
import { Ionicons } from '@expo/vector-icons';

const DebugStorage = () => {
  const router = useRouter();

  const handleGoBack = () => {
    router.back();
  };

  return (
    <ThemedView style={styles.container}>
      {/* 顶部标题和返回按钮 */}
      <ThemedView style={styles.header}>
        <GradientGlassButton
          onPress={handleGoBack}
          borderRadius={12}
          style={styles.backButton}
          gradientColors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)']}
          borderColor="rgba(255, 255, 255, 0.3)"
          blurBackgroundColor="rgba(255, 255, 255, 0.1)"
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </GradientGlassButton>
        
        <ThemedText style={styles.title}>存储调试工具</ThemedText>
      </ThemedView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 存储测试辅助工具 */}
        <StorageTestHelper />

        {/* 存储清理工具 */}
        <StorageCleanupTool />

        {/* 登录状态检查工具 */}
        <LoginStatusChecker />
        
        {/* 使用说明 */}
        <ThemedView style={styles.instructionContainer}>
          <ThemedText style={styles.instructionTitle}>
            🛠️ 调试工具说明
          </ThemedText>
          <ThemedText style={styles.instructionText}>
            这个页面包含了用于调试和管理应用存储的工具：
          </ThemedText>
          <ThemedText style={styles.instructionText}>
            • <ThemedText style={styles.bold}>存储测试辅助工具</ThemedText>：创建测试数据来模拟存储问题
          </ThemedText>
          <ThemedText style={styles.instructionText}>
            • <ThemedText style={styles.bold}>存储清理工具</ThemedText>：管理 profileSetupCompleted 相关的存储数据
          </ThemedText>
          <ThemedText style={styles.instructionText}>
            • <ThemedText style={styles.bold}>登录状态检查工具</ThemedText>：检查和管理用户登录状态
          </ThemedText>
          <ThemedText style={styles.instructionText}>

            使用步骤：1) 先创建测试数据 2) 检查存储状态 3) 使用清理工具清理多余数据
          </ThemedText>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'transparent',
  },
  backButton: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 10,
  },
  instructionContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 20,
    margin: 10,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  instructionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  instructionText: {
    fontSize: 14,
    color: '#FFFFFF',
    lineHeight: 20,
    marginBottom: 8,
    opacity: 0.9,
  },
  bold: {
    fontWeight: 'bold',
  },
});

export default DebugStorage;
