// components/ImageTestTool.jsx - 图片加载测试工具
import React, { useState } from 'react';
import { View, TouchableOpacity, Platform, Alert } from 'react-native';
import ThemedText from './ThemedText';

const ImageTestTool = () => {
  const [testResults, setTestResults] = useState([]);
  const [testing, setTesting] = useState(false);

  // 测试图片URL列表
  const testUrls = [
    'http://localhost:8080/images/tag_1.png',
    'http://localhost:8080/images/tag_2.png',
    'http://localhost:8080/images/tag_3.png',
    'http://localhost:8080/images/tag_4.png',
    'http://localhost:8080/images/tag_5.png',
  ];

  // 测试单个图片URL
  const testImageUrl = async (url) => {
    return new Promise((resolve) => {
      const img = new Image();
      const startTime = Date.now();
      
      img.onload = () => {
        const loadTime = Date.now() - startTime;
        resolve({
          url,
          success: true,
          loadTime,
          error: null,
          dimensions: `${img.naturalWidth}x${img.naturalHeight}`
        });
      };
      
      img.onerror = (error) => {
        const loadTime = Date.now() - startTime;
        resolve({
          url,
          success: false,
          loadTime,
          error: error.message || 'Failed to load',
          dimensions: null
        });
      };
      
      // 设置超时
      setTimeout(() => {
        if (!img.complete) {
          resolve({
            url,
            success: false,
            loadTime: Date.now() - startTime,
            error: 'Timeout',
            dimensions: null
          });
        }
      }, 5000);
      
      img.src = url;
    });
  };

  // 测试后端连接
  const testBackendConnection = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/health', {
        method: 'GET',
        timeout: 5000,
      });
      
      if (response.ok) {
        return { success: true, status: response.status, message: 'Backend is running' };
      } else {
        return { success: false, status: response.status, message: `HTTP ${response.status}` };
      }
    } catch (error) {
      return { success: false, status: null, message: error.message };
    }
  };

  // 运行所有测试
  const runTests = async () => {
    setTesting(true);
    setTestResults([]);
    
    try {
      console.log('🔍 开始图片加载测试...');
      
      // 测试后端连接
      console.log('🔍 测试后端连接...');
      const backendTest = await testBackendConnection();
      console.log('🔍 后端连接测试结果:', backendTest);
      
      // 测试图片加载
      console.log('🔍 测试图片加载...');
      const results = [];
      
      for (const url of testUrls) {
        console.log(`🔍 测试图片: ${url}`);
        const result = await testImageUrl(url);
        console.log(`🔍 图片测试结果:`, result);
        results.push(result);
      }
      
      // 汇总结果
      const summary = {
        total: results.length,
        success: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        avgLoadTime: results.reduce((sum, r) => sum + r.loadTime, 0) / results.length,
        backendStatus: backendTest
      };
      
      console.log('🔍 测试汇总:', summary);
      console.log('🔍 详细结果:', results);
      
      setTestResults([
        { type: 'summary', data: summary },
        { type: 'backend', data: backendTest },
        ...results.map(r => ({ type: 'image', data: r }))
      ]);
      
      // 显示简要结果
      if (Platform.OS === 'web') {
        const message = `测试完成!\n成功: ${summary.success}/${summary.total}\n后端状态: ${backendTest.success ? '✅' : '❌'}\n平均加载时间: ${summary.avgLoadTime.toFixed(0)}ms`;
        Alert.alert('图片测试结果', message);
      }
      
    } catch (error) {
      console.error('🔍 测试过程中出错:', error);
      setTestResults([{ type: 'error', data: { message: error.message } }]);
    } finally {
      setTesting(false);
    }
  };

  // 渲染测试结果
  const renderTestResult = (result, index) => {
    const { type, data } = result;
    
    if (type === 'summary') {
      return (
        <View key={index} style={styles.resultCard}>
          <ThemedText style={styles.resultTitle}>📊 测试汇总</ThemedText>
          <ThemedText style={styles.resultText}>总数: {data.total}</ThemedText>
          <ThemedText style={styles.resultText}>成功: {data.success}</ThemedText>
          <ThemedText style={styles.resultText}>失败: {data.failed}</ThemedText>
          <ThemedText style={styles.resultText}>平均加载时间: {data.avgLoadTime.toFixed(0)}ms</ThemedText>
        </View>
      );
    }
    
    if (type === 'backend') {
      return (
        <View key={index} style={styles.resultCard}>
          <ThemedText style={styles.resultTitle}>🔗 后端连接</ThemedText>
          <ThemedText style={[styles.resultText, { color: data.success ? '#28a745' : '#dc3545' }]}>
            状态: {data.success ? '✅ 正常' : '❌ 异常'}
          </ThemedText>
          <ThemedText style={styles.resultText}>消息: {data.message}</ThemedText>
          {data.status && <ThemedText style={styles.resultText}>HTTP状态: {data.status}</ThemedText>}
        </View>
      );
    }
    
    if (type === 'image') {
      return (
        <View key={index} style={styles.resultCard}>
          <ThemedText style={styles.resultTitle}>🖼️ 图片测试</ThemedText>
          <ThemedText style={styles.resultUrl}>{data.url}</ThemedText>
          <ThemedText style={[styles.resultText, { color: data.success ? '#28a745' : '#dc3545' }]}>
            状态: {data.success ? '✅ 成功' : '❌ 失败'}
          </ThemedText>
          <ThemedText style={styles.resultText}>加载时间: {data.loadTime}ms</ThemedText>
          {data.dimensions && <ThemedText style={styles.resultText}>尺寸: {data.dimensions}</ThemedText>}
          {data.error && <ThemedText style={styles.resultError}>错误: {data.error}</ThemedText>}
        </View>
      );
    }
    
    if (type === 'error') {
      return (
        <View key={index} style={styles.resultCard}>
          <ThemedText style={styles.resultTitle}>❌ 测试错误</ThemedText>
          <ThemedText style={styles.resultError}>{data.message}</ThemedText>
        </View>
      );
    }
    
    return null;
  };

  return (
    <View style={styles.container}>
      <ThemedText style={styles.title}>🔍 图片加载测试工具</ThemedText>
      
      <TouchableOpacity
        onPress={runTests}
        disabled={testing}
        style={[styles.button, { backgroundColor: testing ? '#6c757d' : '#007bff' }]}
      >
        <ThemedText style={styles.buttonText}>
          {testing ? '测试中...' : '开始测试'}
        </ThemedText>
      </TouchableOpacity>
      
      {testResults.length > 0 && (
        <View style={styles.resultsContainer}>
          <ThemedText style={styles.resultsTitle}>测试结果:</ThemedText>
          {testResults.map(renderTestResult)}
        </View>
      )}
      
      <View style={styles.infoContainer}>
        <ThemedText style={styles.infoTitle}>💡 使用说明:</ThemedText>
        <ThemedText style={styles.infoText}>
          • 点击"开始测试"检查图片加载状态{'\n'}
          • 测试结果会显示在控制台和下方{'\n'}
          • 检查后端连接和图片可访问性{'\n'}
          • 如果图片加载失败，请检查后端服务
        </ThemedText>
      </View>
    </View>
  );
};

const styles = {
  container: {
    backgroundColor: '#f8f9fa',
    padding: 20,
    margin: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#dee2e6'
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#495057'
  },
  button: {
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14
  },
  resultsContainer: {
    marginBottom: 15
  },
  resultsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#495057'
  },
  resultCard: {
    backgroundColor: '#ffffff',
    padding: 12,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#dee2e6',
    marginBottom: 8
  },
  resultTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#495057'
  },
  resultText: {
    fontSize: 11,
    color: '#6c757d',
    marginBottom: 2
  },
  resultUrl: {
    fontSize: 10,
    color: '#007bff',
    marginBottom: 5,
    fontFamily: 'monospace'
  },
  resultError: {
    fontSize: 11,
    color: '#dc3545',
    marginBottom: 2
  },
  infoContainer: {
    backgroundColor: '#e9ecef',
    padding: 10,
    borderRadius: 5
  },
  infoTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#6c757d',
    marginBottom: 5
  },
  infoText: {
    fontSize: 11,
    color: '#6c757d',
    lineHeight: 16
  }
};

export default ImageTestTool;
