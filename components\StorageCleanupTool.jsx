// components/StorageCleanupTool.jsx - 存储清理工具组件
import React, { useState } from 'react';
import { View, TouchableOpacity, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ThemedText from './ThemedText';

const StorageCleanupTool = () => {
  const [loading, setLoading] = useState(false);
  const [statusInfo, setStatusInfo] = useState('');
  const [storageData, setStorageData] = useState(null);

  // 检查存储状态
  const handleCheckStorage = async () => {
    setLoading(true);
    try {
      console.log(`[${Platform.OS}] 开始检查存储状态`);

      // 获取所有存储的键
      const allKeys = await AsyncStorage.getAllKeys();
      
      // 筛选出所有 profileSetupCompleted 相关的键
      const profileKeys = allKeys.filter(key => key.startsWith('profileSetupCompleted'));
      
      // 获取其他重要的键
      const importantKeys = ['userAccount', 'auth_token', 'lastVisitedRoute'];
      const otherImportantKeys = allKeys.filter(key => importantKeys.includes(key));
      
      // 获取所有 profileSetupCompleted 条目的详细信息
      const profileDetails = await Promise.all(
        profileKeys.map(async (key) => {
          try {
            const value = await AsyncStorage.getItem(key);
            return { key, value, userAccount: key.replace('profileSetupCompleted_', '') };
          } catch (error) {
            return { key, value: 'ERROR', userAccount: 'ERROR' };
          }
        })
      );

      // 获取其他重要键的值
      const otherDetails = await Promise.all(
        otherImportantKeys.map(async (key) => {
          try {
            const value = await AsyncStorage.getItem(key);
            return { key, value };
          } catch (error) {
            return { key, value: 'ERROR' };
          }
        })
      );

      const data = {
        profileKeys: profileDetails,
        otherKeys: otherDetails,
        totalKeys: allKeys.length,
        profileCount: profileKeys.length
      };

      setStorageData(data);
      
      const info = `存储检查完成:
总键数: ${data.totalKeys}
ProfileSetupCompleted 条目: ${data.profileCount}
其他重要键: ${data.otherKeys.length}`;

      setStatusInfo(info);
      console.log(`[${Platform.OS}] 存储状态检查完成:`, data);
    } catch (error) {
      console.error(`[${Platform.OS}] 检查存储状态失败:`, error);
      setStatusInfo(`检查失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 清理 profileSetupCompleted 存储
  const handleCleanupStorage = async () => {
    setLoading(true);
    try {
      console.log(`[${Platform.OS}] 开始清理存储`);

      // 获取所有存储的键
      const allKeys = await AsyncStorage.getAllKeys();
      
      // 筛选出所有 profileSetupCompleted 相关的键
      const profileKeys = allKeys.filter(key => key.startsWith('profileSetupCompleted'));
      
      if (profileKeys.length <= 3) {
        setStatusInfo('存储条目数量合理，无需清理');
        return;
      }
      
      // 获取当前用户账号（如果存在）
      const currentUserAccount = await AsyncStorage.getItem("userAccount");
      const currentUserKey = currentUserAccount ? `profileSetupCompleted_${currentUserAccount}` : null;
      
      // 保留当前用户的键和最近的2个其他用户的键
      const keysToKeep = [];
      
      // 首先保留当前用户的键
      if (currentUserKey && profileKeys.includes(currentUserKey)) {
        keysToKeep.push(currentUserKey);
      }
      
      // 然后保留其他最近的2个键（排除当前用户的键）
      const otherKeys = profileKeys
        .filter(key => key !== currentUserKey)
        .slice(-2); // 保留最后2个
      
      keysToKeep.push(...otherKeys);
      
      // 找出需要删除的键
      const keysToDelete = profileKeys.filter(key => !keysToKeep.includes(key));
      
      if (keysToDelete.length > 0) {
        // 批量删除多余的键
        await AsyncStorage.multiRemove(keysToDelete);
        
        const info = `清理完成:
删除了 ${keysToDelete.length} 个多余条目
保留了 ${keysToKeep.length} 个条目
删除的键: ${keysToDelete.join(', ')}
保留的键: ${keysToKeep.join(', ')}`;
        
        setStatusInfo(info);
        console.log(`[${Platform.OS}] 存储清理完成，删除了 ${keysToDelete.length} 个条目`);
        
        // 重新检查存储状态
        setTimeout(() => handleCheckStorage(), 500);
      } else {
        setStatusInfo('没有需要清理的条目');
      }

    } catch (error) {
      console.error(`[${Platform.OS}] 清理存储失败:`, error);
      setStatusInfo(`清理失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 清除所有 profileSetupCompleted 数据（危险操作）
  const handleClearAllProfile = async () => {
    setLoading(true);
    try {
      console.log(`[${Platform.OS}] 开始清除所有 profileSetupCompleted 数据`);

      const allKeys = await AsyncStorage.getAllKeys();
      const profileKeys = allKeys.filter(key => key.startsWith('profileSetupCompleted'));
      
      if (profileKeys.length > 0) {
        await AsyncStorage.multiRemove(profileKeys);
        setStatusInfo(`已清除所有 ${profileKeys.length} 个 profileSetupCompleted 条目`);
        console.log(`[${Platform.OS}] 已清除所有 profileSetupCompleted 数据`);
        
        // 重新检查存储状态
        setTimeout(() => handleCheckStorage(), 500);
      } else {
        setStatusInfo('没有找到 profileSetupCompleted 条目');
      }

    } catch (error) {
      console.error(`[${Platform.OS}] 清除所有数据失败:`, error);
      setStatusInfo(`清除失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{
      backgroundColor: '#f8f9fa',
      padding: 20,
      margin: 10,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#dee2e6'
    }}>
      <ThemedText style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 15 }}>
        存储清理工具
      </ThemedText>

      {/* 操作按钮 */}
      <View style={{ marginBottom: 15 }}>
        <TouchableOpacity
          onPress={handleCheckStorage}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#007bff',
              marginBottom: 10
            }
          ]}
        >
          <ThemedText style={buttonTextStyle}>
            {loading ? '检查中...' : '检查存储状态'}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleCleanupStorage}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#28a745',
              marginBottom: 10
            }
          ]}
        >
          <ThemedText style={buttonTextStyle}>
            {loading ? '清理中...' : '智能清理存储'}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleClearAllProfile}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#dc3545'
            }
          ]}
        >
          <ThemedText style={buttonTextStyle}>
            {loading ? '清除中...' : '清除所有Profile数据'}
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* 存储数据显示 */}
      {storageData && (
        <View style={{
          backgroundColor: '#ffffff',
          padding: 12,
          borderRadius: 5,
          borderWidth: 1,
          borderColor: '#dee2e6',
          marginBottom: 10
        }}>
          <ThemedText style={{ fontWeight: 'bold', marginBottom: 8 }}>
            ProfileSetupCompleted 条目 ({storageData.profileCount}):
          </ThemedText>
          
          {storageData.profileKeys.map((item, index) => (
            <ThemedText key={index} style={{
              fontSize: 11,
              fontFamily: 'monospace',
              color: '#495057',
              marginBottom: 2
            }}>
              {item.userAccount}: {item.value}
            </ThemedText>
          ))}
          
          {storageData.otherKeys.length > 0 && (
            <>
              <ThemedText style={{ fontWeight: 'bold', marginTop: 10, marginBottom: 5 }}>
                其他重要键:
              </ThemedText>
              {storageData.otherKeys.map((item, index) => (
                <ThemedText key={index} style={{
                  fontSize: 11,
                  fontFamily: 'monospace',
                  color: '#495057',
                  marginBottom: 2
                }}>
                  {item.key}: {item.value || '(空)'}
                </ThemedText>
              ))}
            </>
          )}
        </View>
      )}

      {/* 状态信息 */}
      {statusInfo && (
        <View style={{
          backgroundColor: '#ffffff',
          padding: 12,
          borderRadius: 5,
          borderWidth: 1,
          borderColor: '#dee2e6',
          marginBottom: 10
        }}>
          <ThemedText style={{
            fontSize: 11,
            fontFamily: 'monospace',
            color: '#495057',
            lineHeight: 16
          }}>
            {statusInfo}
          </ThemedText>
        </View>
      )}

      {/* 使用说明 */}
      <View style={{
        backgroundColor: '#e9ecef',
        padding: 10,
        borderRadius: 5
      }}>
        <ThemedText style={{ fontSize: 12, color: '#6c757d' }}>
          💡 使用说明：
        </ThemedText>
        <ThemedText style={{ fontSize: 11, color: '#6c757d', marginTop: 5 }}>
          • 检查存储状态：查看当前所有 profileSetupCompleted 条目{'\n'}
          • 智能清理存储：保留当前用户和最近2个用户的数据{'\n'}
          • 清除所有Profile数据：删除所有 profileSetupCompleted 条目（危险操作）
        </ThemedText>
      </View>
    </View>
  );
};

const buttonStyle = {
  padding: 12,
  borderRadius: 6,
  alignItems: 'center',
  justifyContent: 'center'
};

const buttonTextStyle = {
  color: 'white',
  fontWeight: 'bold',
  fontSize: 14
};

export default StorageCleanupTool;
