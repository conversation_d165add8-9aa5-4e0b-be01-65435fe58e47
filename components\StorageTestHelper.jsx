// components/StorageTestHelper.jsx - 存储测试辅助工具
import React, { useState } from 'react';
import { View, TouchableOpacity, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ThemedText from './ThemedText';

const StorageTestHelper = () => {
  const [loading, setLoading] = useState(false);
  const [statusInfo, setStatusInfo] = useState('');

  // 创建测试数据
  const handleCreateTestData = async () => {
    setLoading(true);
    try {
      console.log(`[${Platform.OS}] 开始创建测试数据`);

      // 创建多个测试用户的 profileSetupCompleted 条目
      const testUsers = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        'user123',
        'demo_user',
        'guest_user_001',
        'guest_user_002'
      ];

      // 为每个测试用户创建 profileSetupCompleted 条目
      const testData = testUsers.map((userAccount, index) => [
        `profileSetupCompleted_${userAccount}`,
        index % 2 === 0 ? 'true' : 'false' // 交替设置为 true/false
      ]);

      // 批量设置测试数据
      await AsyncStorage.multiSet(testData);

      const info = `测试数据创建完成:
创建了 ${testUsers.length} 个 profileSetupCompleted 条目
用户账号: ${testUsers.join(', ')}`;

      setStatusInfo(info);
      console.log(`[${Platform.OS}] 测试数据创建完成，创建了 ${testUsers.length} 个条目`);
    } catch (error) {
      console.error(`[${Platform.OS}] 创建测试数据失败:`, error);
      setStatusInfo(`创建失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 创建当前用户数据
  const handleCreateCurrentUserData = async () => {
    setLoading(true);
    try {
      console.log(`[${Platform.OS}] 开始创建当前用户数据`);

      const currentTime = new Date().toISOString();
      const currentUserAccount = `current_user_${Date.now()}`;

      // 设置当前用户账号
      await AsyncStorage.setItem('userAccount', currentUserAccount);
      
      // 设置当前用户的 profileSetupCompleted 状态
      await AsyncStorage.setItem(`profileSetupCompleted_${currentUserAccount}`, 'true');

      const info = `当前用户数据创建完成:
用户账号: ${currentUserAccount}
ProfileSetupCompleted: true
创建时间: ${currentTime}`;

      setStatusInfo(info);
      console.log(`[${Platform.OS}] 当前用户数据创建完成:`, currentUserAccount);
    } catch (error) {
      console.error(`[${Platform.OS}] 创建当前用户数据失败:`, error);
      setStatusInfo(`创建失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 模拟退出登录场景
  const handleSimulateLogout = async () => {
    setLoading(true);
    try {
      console.log(`[${Platform.OS}] 开始模拟退出登录`);

      // 获取当前用户账号
      const currentUserAccount = await AsyncStorage.getItem('userAccount');
      
      if (!currentUserAccount) {
        setStatusInfo('没有找到当前用户账号，请先创建当前用户数据');
        return;
      }

      // 模拟清理多余的 profileSetupCompleted 条目
      const allKeys = await AsyncStorage.getAllKeys();
      const profileKeys = allKeys.filter(key => key.startsWith('profileSetupCompleted'));
      
      console.log(`找到 ${profileKeys.length} 个 profileSetupCompleted 条目`);
      
      if (profileKeys.length <= 3) {
        setStatusInfo('存储条目数量合理，无需清理');
        return;
      }
      
      const currentUserKey = `profileSetupCompleted_${currentUserAccount}`;
      
      // 保留当前用户的键和最近的2个其他用户的键
      const keysToKeep = [];
      
      if (profileKeys.includes(currentUserKey)) {
        keysToKeep.push(currentUserKey);
      }
      
      const otherKeys = profileKeys
        .filter(key => key !== currentUserKey)
        .slice(-2);
      
      keysToKeep.push(...otherKeys);
      
      const keysToDelete = profileKeys.filter(key => !keysToKeep.includes(key));
      
      if (keysToDelete.length > 0) {
        await AsyncStorage.multiRemove(keysToDelete);
        
        const info = `模拟退出登录清理完成:
当前用户: ${currentUserAccount}
删除了 ${keysToDelete.length} 个多余条目
保留了 ${keysToKeep.length} 个条目
删除的键: ${keysToDelete.map(k => k.replace('profileSetupCompleted_', '')).join(', ')}
保留的键: ${keysToKeep.map(k => k.replace('profileSetupCompleted_', '')).join(', ')}`;
        
        setStatusInfo(info);
        console.log(`[${Platform.OS}] 模拟退出登录清理完成，删除了 ${keysToDelete.length} 个条目`);
      } else {
        setStatusInfo('没有需要清理的条目');
      }

      // 模拟清除用户账号（但保留 profileSetupCompleted 数据）
      await AsyncStorage.removeItem('userAccount');
      await AsyncStorage.removeItem('auth_token');

    } catch (error) {
      console.error(`[${Platform.OS}] 模拟退出登录失败:`, error);
      setStatusInfo(`模拟失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{
      backgroundColor: '#fff3cd',
      padding: 20,
      margin: 10,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#ffeaa7'
    }}>
      <ThemedText style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 15, color: '#856404' }}>
        存储测试辅助工具
      </ThemedText>

      {/* 操作按钮 */}
      <View style={{ marginBottom: 15 }}>
        <TouchableOpacity
          onPress={handleCreateTestData}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#ffc107',
              marginBottom: 10
            }
          ]}
        >
          <ThemedText style={[buttonTextStyle, { color: '#212529' }]}>
            {loading ? '创建中...' : '创建测试数据 (12个条目)'}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleCreateCurrentUserData}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#17a2b8',
              marginBottom: 10
            }
          ]}
        >
          <ThemedText style={buttonTextStyle}>
            {loading ? '创建中...' : '创建当前用户数据'}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleSimulateLogout}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#fd7e14'
            }
          ]}
        >
          <ThemedText style={buttonTextStyle}>
            {loading ? '模拟中...' : '模拟退出登录清理'}
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* 状态信息 */}
      {statusInfo && (
        <View style={{
          backgroundColor: '#ffffff',
          padding: 12,
          borderRadius: 5,
          borderWidth: 1,
          borderColor: '#ffeaa7',
          marginBottom: 10
        }}>
          <ThemedText style={{
            fontSize: 11,
            fontFamily: 'monospace',
            color: '#495057',
            lineHeight: 16
          }}>
            {statusInfo}
          </ThemedText>
        </View>
      )}

      {/* 使用说明 */}
      <View style={{
        backgroundColor: '#fff8e1',
        padding: 10,
        borderRadius: 5
      }}>
        <ThemedText style={{ fontSize: 12, color: '#856404' }}>
          ⚠️ 测试工具说明：
        </ThemedText>
        <ThemedText style={{ fontSize: 11, color: '#856404', marginTop: 5 }}>
          • 创建测试数据：生成12个假的 profileSetupCompleted 条目{'\n'}
          • 创建当前用户数据：设置一个当前用户账号和对应的 profile 状态{'\n'}
          • 模拟退出登录清理：测试清理逻辑，保留当前用户和最近2个用户的数据
        </ThemedText>
      </View>
    </View>
  );
};

const buttonStyle = {
  padding: 12,
  borderRadius: 6,
  alignItems: 'center',
  justifyContent: 'center'
};

const buttonTextStyle = {
  color: 'white',
  fontWeight: 'bold',
  fontSize: 14
};

export default StorageTestHelper;
